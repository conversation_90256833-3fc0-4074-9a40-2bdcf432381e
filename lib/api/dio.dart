import 'dart:convert';
import 'dart:io';

import 'package:agnostic_healthcare/utils/shared_prefs.dart';
import 'package:dio/dio.dart';

var timeOutResponse = Response(
  requestOptions: RequestOptions(),
  statusCode: HttpStatus.gatewayTimeout,
  statusMessage: 'Internet connection Timeout',
);
var shortTimeoutDuration = const Duration(seconds: 30);
var mediumTimeoutDuration = const Duration(seconds: 30);
var longTimeoutDuration = const Duration(minutes: 5);

class AppDio {
  String baseUrl;

  AppDio(this.baseUrl);

  String _parseError(DioException e) {
    if (e.response?.statusCode == HttpStatus.gatewayTimeout) {
      return 'Oops! We\'re experiencing server issues at the moment.';
    } else if (e.response?.statusCode == 403 &&
        e.requestOptions.path == baseUrl) {
      return json.encode({
        'block_reason':
            e.response?.data?['message'] ??
            e.response?.statusMessage ??
            'Unable to login',
      });
    } else if (e.response?.statusCode == HttpStatus.notFound) {
      return 'Unable to connect to server';
    } else if (e.error is SocketException) {
      return 'Internet not available';
    } else {
      if (e.type == DioExceptionType.badResponse &&
          e.response != null &&
          e.response!.data.runtimeType == String) {
        return 'Server error';
      } else if (e.response != null && e.response!.data['errors'] != null) {
        List<dynamic> errors =
            e.response?.data['errors'].values
                .toList()
                .map((e) => e[0])
                .toList();
        return errors.join(', ');
      } else {
        return (e.response?.data['error'] ??
                e.response?.data['message'] ??
                e.message ??
                '')
            .toString();
      }
    }
  }

  Future<void> postApiResponse(
    String url, {
    Map<String, dynamic>? requestData,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    Function(int, int)? progressReceiver,
    bool convertToFormData = false,
    bool jsonEncodeData = false,
    required Function(String, dynamic) onSuccess,
    required Function(String) onError,
    required Duration timeoutDuration,
  }) async {
    try {
      queryParameters?.removeWhere((k, v) => v == null);
      requestData?.removeWhere((k, v) => v == null);
      Response result = await Dio(BaseOptions(baseUrl: baseUrl))
          .post(
            url,
            onSendProgress: progressReceiver,
            queryParameters: queryParameters,
            data:
                convertToFormData && requestData != null
                    ? FormData.fromMap(requestData, ListFormat.multiCompatible)
                    : jsonEncodeData
                    ? jsonEncode(requestData)
                    : requestData,
            options: Options(
              headers:
                  headers ??
                  (SharedPrefs().savedUserToken.isNotEmpty
                      ? {
                        'Authorization':
                            'Bearer ${SharedPrefs().savedUserToken}',
                      }
                      : {}),
            ),
          )
          .timeout(timeoutDuration, onTimeout: () => timeOutResponse);
      if (result.statusCode == HttpStatus.ok ||
          result.statusCode == HttpStatus.created) {
        String message = result.data?['message'] ?? '';
        onSuccess(message, result.data);
      } else {
        onError(result.data?['message'] ?? result.statusMessage);
      }
    } on DioException catch (e) {
      onError(_parseError(e));
    } catch (e) {
      onError(e.toString());
    }
  }

  Future<void> getApiResponse(
    String url, {
    Map<String, dynamic>? queryParameters,
    bool convertToFormData = false,
    required Function(String, dynamic) onSuccess,
    required Function(String) onError,
    required Duration timeoutDuration,
  }) async {
    try {
      Response result = await Dio(BaseOptions(baseUrl: baseUrl))
          .get(
            url,
            queryParameters: queryParameters,
            options: Options(
              headers:
                  SharedPrefs().savedUserToken.isNotEmpty
                      ? {
                        'Authorization':
                            'Bearer ${SharedPrefs().savedUserToken}',
                      }
                      : {},
            ),
          )
          .timeout(timeoutDuration, onTimeout: () => timeOutResponse);
      if (result.statusCode == HttpStatus.ok ||
          result.statusCode == HttpStatus.created) {
        String message = result.data?['message'] ?? '';
        onSuccess(message, result.data);
      } else {
        onError(result.data?['message'] ?? result.statusMessage);
      }
    } on DioException catch (e) {
      onError(_parseError(e));
    } catch (e) {
      onError(e.toString());
    }
  }

  Future<void> deleteApiResponse(
    String url, {
    required Function(String, dynamic) onSuccess,
    required Function(String) onError,
    required Duration timeoutDuration,
  }) async {
    try {
      Response result = await Dio(BaseOptions(baseUrl: baseUrl))
          .delete(
            url,
            options: Options(
              headers:
                  SharedPrefs().savedUserToken.isNotEmpty
                      ? {
                        'Authorization':
                            'Bearer ${SharedPrefs().savedUserToken}',
                      }
                      : {},
            ),
          )
          .timeout(timeoutDuration, onTimeout: () => timeOutResponse);
      if (result.statusCode == HttpStatus.ok ||
          result.statusCode == HttpStatus.created) {
        String message = result.data?['message'];
        onSuccess(message, result.data);
      } else {
        onError(result.data?['message'] ?? result.statusMessage);
      }
    } on DioException catch (e) {
      onError(_parseError(e));
    } catch (e) {
      onError(e.toString());
    }
  }
}
