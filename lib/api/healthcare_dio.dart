import 'package:agnostic_healthcare/api/dio.dart';

class BagTalkDio {
  AppDio dio = AppDio('');

  // Future<void> getProfile({
  //   required Function() onSuccess,
  //   required Function(String) onError,
  // }) async => dio.getApiResponse(
  //   ApiPaths.profile,
  //   onSuccess: (message, data) {
  //     onSuccess(user);
  //   },
  //   onError: onError,
  //   timeoutDuration: shortTimeoutDuration,
  // );
}
