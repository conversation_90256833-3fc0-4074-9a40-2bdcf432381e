import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:table_calendar/table_calendar.dart';

class CustomColors {
  static const Color theme = Color(0xFF73A127);
  static const Color themeLight = Color(0xFF8CC331);
  static const Color themeUltraLight = Color(0xFFBAE96E);
  static const Color themeWhite = Color(0xFFEEEEEE);
  static const Color themeCardBgLight = Color(0xFFF1FFD9);
  static const Color canvas = Color(0xFFF4F4F2);
  static const Color lightBlackBackground = Color(0xff3F3F3F);
  static const Color lightGreyBackground = Color(0xff848482);
  static const Color greyBackground = Color(0xffAEB0B3);

  static const Color text = Color(0xff262626);
  static const Color textLight = Color(0xff999999);
  static const Color textSecondaryLight = Color(0xffD9D9D9);
  static const Color textLightGrey = Color(0xffEBEBEB);
  static const Color textWhite = Color(0xffFFFFFF);
  static const Color textGrey = Color(0xff3A3B3C);
  static const Color charCoalBlack = Color(0xff4E4E4C);

  static const Color divider = Color(0xFFCFD6CF);
  static const Color yellow = Color(0xffFF6200);
  static const Color red = Color(0xffEF3739);
  static const Color success = Color(0xff008000);
  static const Color bgBlurColor = Color(0xFF9F9F9F);
}

class CustomDropdownStyle {
  static get menuStyleData => MenuItemStyleData(height: 50.spMin);

  static get buttonStyleData => ButtonStyleData(
    padding: EdgeInsets.symmetric(horizontal: 0.spMin),
    height: 48.spMin,
    decoration: BoxDecoration(
      color: CustomColors.textWhite,
      borderRadius: AppRoundBorder.circularXpx(),
    ),
    width: double.infinity,
  );

  static get iconStyleDataSmall => IconStyleData(
    icon: SvgPicture.asset('assets/icons/arrow_down.svg', height: 6.spMin),
  );

  static dropdownStyleData({double? width, EdgeInsets? padding}) =>
      DropdownStyleData(
        padding: padding,
        width: width,
        maxHeight: 200.spMin,
        offset: const Offset(0, -5),
        decoration: BoxDecoration(
          borderRadius: AppRoundBorder.circularXpx(),
          color: CustomColors.textWhite,
        ),
        scrollbarTheme: ScrollbarThemeData(
          radius: const Radius.circular(40),
          thickness: WidgetStateProperty.all(6),
          thumbVisibility: WidgetStateProperty.all(true),
        ),
      );
}

class AppTextStyle {
  static styleSansLight(
    double? fontSize, {
    Color fontColor = CustomColors.text,
    double? height,
    FontStyle? fontStyle,
    TextDecoration? textDecoration,
  }) => TextStyle(
    fontFamily: 'DMSans-ExtraLight',
    fontSize: fontSize?.spMin,
    decoration: textDecoration ?? TextDecoration.none,
    fontStyle: fontStyle,
    height: height,
    decorationColor: CustomColors.text,
    decorationThickness: 1,
    color: fontColor,
  );

  static styleSansSemiBold(
    double? fontSize, {
    Color fontColor = CustomColors.text,
    double? height,
    FontStyle? fontStyle,
    TextDecoration? textDecoration,
  }) => TextStyle(
    fontFamily: 'DMSans-SemiBold',
    fontSize: fontSize?.spMin,
    decoration: textDecoration ?? TextDecoration.none,
    fontStyle: fontStyle,
    height: height,
    decorationColor: CustomColors.text,
    decorationThickness: 1,
    color: fontColor,
  );

  static styleSansRegular(
    double? fontSize, {
    Color fontColor = CustomColors.text,
    double? height,
    double? letterSpacing,
    FontStyle? fontStyle,
    TextDecoration? textDecoration,
  }) => TextStyle(
    fontFamily: 'DMSans-Regular',
    fontSize: fontSize?.spMin,
    decoration: textDecoration ?? TextDecoration.none,
    fontStyle: fontStyle,
    letterSpacing: letterSpacing,
    height: height,
    decorationColor: CustomColors.text,
    decorationThickness: 1,
    color: fontColor,
  );

  static styleSansMedium(
    double? fontSize, {
    Color fontColor = CustomColors.text,
    double? height,
    double? letterSpacing,
    FontStyle? fontStyle,
    TextDecoration? textDecoration,
  }) => TextStyle(
    fontFamily: 'DMSans-Medium',
    fontSize: fontSize?.spMin,
    decoration: textDecoration ?? TextDecoration.none,
    fontStyle: fontStyle,
    letterSpacing: letterSpacing,
    height: height,
    decorationColor: CustomColors.text,
    decorationThickness: 1,
    color: fontColor,
  );

  static styleSansBold(
    double? fontSize, {
    Color fontColor = CustomColors.text,
    double? height,
    FontStyle? fontStyle,
    TextDecoration? textDecoration,
    FontWeight? fontWeight,
  }) => TextStyle(
    fontFamily: 'DMSans-Bold',
    fontSize: fontSize?.spMin,
    decoration: textDecoration ?? TextDecoration.none,
    fontStyle: fontStyle,
    height: height,
    decorationColor: CustomColors.text,
    decorationThickness: 1,
    color: fontColor,
  );
}

class AppSpacing {
  static EdgeInsets get leftRightSpacingLarge =>
      EdgeInsets.symmetric(horizontal: 24.spMin, vertical: 0);

  static EdgeInsets get leftRightSpacingSmall =>
      EdgeInsets.symmetric(horizontal: 12.spMin, vertical: 0);

  static EdgeInsets get pageVerticalPadding =>
      EdgeInsets.symmetric(vertical: 20.spMin);

  static EdgeInsets get pageVerticalSmallPadding =>
      EdgeInsets.symmetric(vertical: 8.spMin);

  static EdgeInsets get pagePaddingLarge =>
      EdgeInsets.symmetric(vertical: 52.spMin, horizontal: 24.spMin);

  static EdgeInsets get pagePaddingXLarge => EdgeInsets.all(30.spMin);

  static EdgeInsets get pagePaddingSmall => EdgeInsets.all(22.spMin);

  static EdgeInsets get pagePaddingExtraSmall =>
      EdgeInsets.symmetric(vertical: 15.spMin, horizontal: 20.spMin);

  static verticalXSpacing({double space = 10}) => SizedBox(height: space.spMin);

  static horizontalXSpacing({double space = 10}) =>
      SizedBox(width: space.spMin);
}

class AppInputDecoration {
  static InputDecoration planeDecoration({
    String? hintText,
    Widget? leadingWidget,
    Widget? trailingWidget,
    EdgeInsetsGeometry? contentPadding,
    bool showCounter = false,
    bool? isDense,
    TextStyle? hintStyle,
    InputBorder? border,
  }) {
    return InputDecoration(
      isDense: isDense,
      errorStyle: const TextStyle(height: 0, fontSize: 0),
      errorMaxLines: 1,
      prefixIcon: leadingWidget,
      suffixIcon: trailingWidget,
      prefixIconConstraints: BoxConstraints(
        maxWidth: 34.spMin,
        maxHeight: 24.spMin,
      ),
      suffixIconConstraints: BoxConstraints(
        maxWidth: 24.spMin,
        maxHeight: 24.spMin,
      ),
      border: InputBorder.none,
      focusedBorder: InputBorder.none,
      enabledBorder: InputBorder.none,
      errorBorder: InputBorder.none,
      disabledBorder: InputBorder.none,
      contentPadding:
          contentPadding ??
          EdgeInsets.symmetric(vertical: 17.spMin, horizontal: 16.spMin),
      hintText: hintText ?? '',
      counterText: showCounter ? null : '',
      hintStyle:
          hintStyle ??
          AppTextStyle.styleSansMedium(16, fontColor: CustomColors.textLight),
    );
  }
}

class AppRoundBorder {
  static BorderRadius get squareTop15px => const BorderRadius.only(
    bottomLeft: Radius.circular(15),
    topRight: Radius.circular(15),
    bottomRight: Radius.circular(15),
  );

  static BorderRadius get topRound30px => const BorderRadius.only(
    topLeft: Radius.circular(30),
    topRight: Radius.circular(30),
  );

  static BorderRadius get bottomLeftSquareBorder => const BorderRadius.only(
    topLeft: Radius.circular(8),
    topRight: Radius.circular(8),
    bottomRight: Radius.circular(8),
  );

  static BorderRadius get bottomRightSquareBorder => const BorderRadius.only(
    topLeft: Radius.circular(8),
    bottomLeft: Radius.circular(8),
    topRight: Radius.circular(8),
  );

  static BorderRadius get circular16px => BorderRadius.circular(16);

  static BorderRadius get circular20px => BorderRadius.circular(20);

  static BorderRadius circularXpx({double pixel = 7}) =>
      BorderRadius.circular(pixel);
}

class AppGradients {
  static Gradient get newKiwiButtonGradiant => LinearGradient(
    colors: [CustomColors.themeLight, CustomColors.theme],
    stops: [0.0, 1],
  );

  static Gradient get buttonGradiant => LinearGradient(
    colors: [CustomColors.theme, CustomColors.themeLight],
    stops: [0.0, 1],
  );

  static Gradient get splashBackgroundGradient => LinearGradient(
    colors: [CustomColors.textWhite, CustomColors.canvas],
    stops: [0.0, 1],
  );

  static Gradient get stoneBackgroundGradient => LinearGradient(
    colors: [
      CustomColors.lightBlackBackground,
      CustomColors.lightGreyBackground,
    ],
    stops: [0.0, 1],
  );

  static Gradient get upcomingAppGradient => LinearGradient(
    begin: Alignment.topRight,
    end: Alignment.bottomLeft,
    colors: [CustomColors.charCoalBlack, CustomColors.themeLight],
    stops: [0.0, 1],
  );
}

class CustomCalenderStyles {
  static get headerStyle => HeaderStyle(
    titleCentered: true,
    titleTextStyle: AppTextStyle.styleSansRegular(16),
    headerMargin: EdgeInsets.zero,
    leftChevronMargin: EdgeInsets.zero,
    rightChevronMargin: EdgeInsets.zero,
    formatButtonVisible: false,
    headerPadding: EdgeInsets.zero,
    leftChevronIcon: SvgPicture.asset(
      'assets/icons/arrow_left.svg',
      colorFilter: ColorFilter.mode(CustomColors.text, BlendMode.srcIn),
    ),
    rightChevronIcon: SvgPicture.asset(
      'assets/icons/arrow_next.svg',
      colorFilter: ColorFilter.mode(CustomColors.text, BlendMode.srcIn),
    ),
  );

  static get calenderStyle => CalendarStyle(
    todayDecoration: const BoxDecoration(
      color: CustomColors.theme,
      shape: BoxShape.circle,
    ),
    selectedDecoration: const BoxDecoration(
      color: CustomColors.text,
      shape: BoxShape.circle,
    ),
    todayTextStyle: AppTextStyle.styleSansRegular(
      14,
      fontColor: CustomColors.textWhite,
    ),
    defaultTextStyle: AppTextStyle.styleSansRegular(
      14,
      fontColor: CustomColors.text,
    ),
    selectedTextStyle: AppTextStyle.styleSansRegular(
      14,
      fontColor: CustomColors.textWhite,
    ),
    cellMargin: EdgeInsets.all(3.spMin),
    weekendTextStyle: AppTextStyle.styleSansRegular(14),
    outsideTextStyle: AppTextStyle.styleSansRegular(14),
  );

  static get dowStyle => DaysOfWeekStyle(
    weekdayStyle: AppTextStyle.styleSansRegular(
      14,
      fontColor: CustomColors.textGrey,
    ),
    weekendStyle: AppTextStyle.styleSansRegular(
      14,
      fontColor: CustomColors.textGrey,
    ),
  );
}
