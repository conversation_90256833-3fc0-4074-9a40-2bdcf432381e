import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:shared_preferences/shared_preferences.dart';

late SharedPreferences sharedPreferences;
late List<CameraDescription> cameras;

String get profile {
  return _profileImages.first;
  // final random = Random();
  // return _profileImages[random.nextInt(_profileImages.length)];
}

final List<String> _profileImages = [
  'https://randomuser.me/api/portraits/men/1.jpg',
  'https://randomuser.me/api/portraits/women/1.jpg',
  'https://randomuser.me/api/portraits/men/2.jpg',
  'https://randomuser.me/api/portraits/women/2.jpg',
  'https://images.unsplash.com/photo-1531427186611-ecfd6d936c79?w=150',
  'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
];

List<String> get dummyImages => [
  'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR5gLldkekGOor_k2HaiCP_xCLblOItLcyZmA&s',
  'https://images.unsplash.com/photo-1640161704729-cbe966a08476?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Y3J5cHRvY3VycmVuY3l8ZW58MHx8MHx8fDA%3D',
  'https://images.unsplash.com/photo-1518546305927-5a555bb7020d?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8Y3J5cHRvY3VycmVuY3l8ZW58MHx8MHx8fDA%3D',
  'https://images.unsplash.com/photo-1640826514546-7d2eab70a4e5?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fGNyeXB0b2N1cnJlbmN5fGVufDB8fDB8fHww',
  'https://images.unsplash.com/photo-1639762681485-074b7f938ba0?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MjB8fGNyeXB0b2N1cnJlbmN5fGVufDB8fDB8fHww',
];

String get dummyText =>
    'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

PageController bottomNavigationTabController = PageController(
  keepPage: true,
  initialPage: 2,
);

void showErrorMessage({required String error}) {
  if (error.isEmpty) {
    return;
  }
  Fluttertoast.showToast(
    fontSize: 14,
    toastLength: Toast.LENGTH_LONG,
    msg: error,
    backgroundColor: CustomColors.red,
    textColor: CustomColors.textWhite,
  );
}

void showSuccessMessage({required String message, BuildContext? context}) {
  if (message.isEmpty) {
    return;
  }
  Fluttertoast.showToast(
    fontSize: 14,
    toastLength: Toast.LENGTH_SHORT,
    msg: message,
    backgroundColor: CustomColors.success,
    textColor: CustomColors.textWhite,
  );
}
