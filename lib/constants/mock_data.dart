import '../models/models.dart';

class MockData {
  // Mock Users - Different verification levels
  static User get verifiedUser => User(
    id: 'user_001',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+64 21 123 4567',
    profileImage: 'https://example.com/profile1.jpg',
    isEmailVerified: true,
    isPhoneVerified: true,
    isUserIdentityVerified: true,
    createdAt: DateTime.now().subtract(Duration(days: 30)),
    lastLoginAt: DateTime.now().subtract(Duration(hours: 2)),
  );

  static User get partiallyVerifiedUser => User(
    id: 'user_002',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+64 21 987 6543',
    profileImage: 'https://example.com/profile2.jpg',
    isEmailVerified: true,
    isPhoneVerified: true,
    isUserIdentityVerified: false, // Not identity verified
    createdAt: DateTime.now().subtract(Duration(days: 15)),
    lastLoginAt: DateTime.now().subtract(Duration(minutes: 30)),
  );

  static User get unverifiedUser => User(
    id: 'user_003',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+64 21 555 0123',
    profileImage: 'https://example.com/profile3.jpg',
    isEmailVerified: false,
    isPhoneVerified: false,
    isUserIdentityVerified: false,
    createdAt: DateTime.now().subtract(Duration(days: 5)),
    lastLoginAt: DateTime.now().subtract(Duration(hours: 1)),
  );

  // Mock Appointments for verified users
  static List<Appointment> get upcomingAppointments => [
    Appointment(
      id: 'apt_001',
      doctorName: 'Dr. Aaron Smith',
      clinicName: 'ABC Clinic',
      specialty: 'Cardiologist',
      appointmentDate: DateTime.now().add(Duration(days: 2)),
      timeSlot: '10:30 AM - 11:00 AM',
      location: '190M, Auckland, NZ',
      status: AppointmentStatus.confirmed,
      type: AppointmentType.telehealth,
      canJoin: true,
      notes: 'Follow-up consultation for heart health',
    ),
    Appointment(
      id: 'apt_002',
      doctorName: 'Dr. Emily Wilson',
      clinicName: 'Holloway Health Center',
      specialty: 'Dermatologist',
      appointmentDate: DateTime.now().add(Duration(days: 5)),
      timeSlot: '2:00 PM - 2:30 PM',
      location: '250M, Wellington, NZ',
      status: AppointmentStatus.scheduled,
      type: AppointmentType.inPerson,
      canJoin: false,
      notes: 'Skin check-up appointment',
    ),
    Appointment(
      id: 'apt_003',
      doctorName: 'Dr. James Taylor',
      clinicName: 'Central Medical Centre',
      specialty: 'General Practitioner',
      appointmentDate: DateTime.now().add(Duration(days: 7)),
      timeSlot: '9:00 AM - 9:30 AM',
      location: '120M, Christchurch, NZ',
      status: AppointmentStatus.scheduled,
      type: AppointmentType.inPerson,
      canJoin: false,
      notes: 'Annual health check-up',
    ),
  ];

  // Mock Medicine Records
  static List<MedicineRecord> get medicineRecords => [
    MedicineRecord(
      id: 'med_001',
      medicationName: 'Paracetamol',
      dosage: '500mg',
      frequency: 'Twice daily',
      prescribedBy: 'Dr. Nathan Williams',
      prescribedDate: DateTime.now().subtract(Duration(days: 10)),
      endDate: DateTime.now().add(Duration(days: 5)),
      instructions: 'Take with food. Do not exceed 8 tablets in 24 hours.',
      status: MedicationStatus.active,
      type: MedicationType.prescription,
    ),
    MedicineRecord(
      id: 'med_002',
      medicationName: 'Vitamin D3',
      dosage: '1000 IU',
      frequency: 'Once daily',
      prescribedBy: 'Dr. Sarah Mitchell',
      prescribedDate: DateTime.now().subtract(Duration(days: 30)),
      endDate: DateTime.now().add(Duration(days: 60)),
      instructions: 'Take with breakfast for better absorption.',
      status: MedicationStatus.active,
      type: MedicationType.supplement,
    ),
    MedicineRecord(
      id: 'med_003',
      medicationName: 'Ibuprofen',
      dosage: '200mg',
      frequency: 'As needed',
      prescribedBy: 'Dr. Robert Chen',
      prescribedDate: DateTime.now().subtract(Duration(days: 5)),
      endDate: DateTime.now().add(Duration(days: 10)),
      instructions: 'Take with food. Maximum 3 times per day.',
      status: MedicationStatus.active,
      type: MedicationType.prescription,
    ),
  ];

  // Mock Past Appointments
  static List<Appointment> get pastAppointments => [
    Appointment(
      id: 'apt_past_001',
      doctorName: 'Dr. Lisa Anderson',
      clinicName: 'City Health Clinic',
      specialty: 'General Practitioner',
      appointmentDate: DateTime.now().subtract(Duration(days: 15)),
      timeSlot: '11:00 AM - 11:30 AM',
      location: '180M, Auckland, NZ',
      status: AppointmentStatus.completed,
      type: AppointmentType.inPerson,
      canJoin: false,
      notes: 'Routine check-up completed successfully',
    ),
    Appointment(
      id: 'apt_past_002',
      doctorName: 'Dr. Mark Thompson',
      clinicName: 'Wellness Medical Center',
      specialty: 'Cardiologist',
      appointmentDate: DateTime.now().subtract(Duration(days: 30)),
      timeSlot: '3:00 PM - 3:45 PM',
      location: '200M, Wellington, NZ',
      status: AppointmentStatus.completed,
      type: AppointmentType.telehealth,
      canJoin: false,
      notes: 'Heart health assessment completed',
    ),
  ];

  // Helper methods to get data based on user verification status
  static List<Appointment> getAppointmentsForUser(User user) {
    if (user.isFullyVerified) {
      return upcomingAppointments;
    }
    return []; // Unverified users don't have appointments
  }

  static List<MedicineRecord> getMedicineRecordsForUser(User user) {
    if (user.isFullyVerified) {
      return medicineRecords;
    }
    return []; // Unverified users don't have medicine records
  }

  static Appointment? getNextAppointmentForUser(User user) {
    final appointments = getAppointmentsForUser(user);
    if (appointments.isEmpty) return null;
    
    // Return the nearest upcoming appointment
    appointments.sort((a, b) => a.appointmentDate.compareTo(b.appointmentDate));
    return appointments.first;
  }

  static MedicineRecord? getRecentMedicineRecordForUser(User user) {
    final records = getMedicineRecordsForUser(user);
    if (records.isEmpty) return null;
    
    // Return the most recently prescribed medicine
    records.sort((a, b) => b.prescribedDate.compareTo(a.prescribedDate));
    return records.first;
  }

  // Mock user selection for testing different scenarios
  static User getCurrentMockUser({bool isVerified = true}) {
    if (isVerified) {
      return verifiedUser;
    } else {
      return unverifiedUser;
    }
  }
}
