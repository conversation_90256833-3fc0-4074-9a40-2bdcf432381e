import 'dart:io';

import 'package:agnostic_healthcare/constants/data_constants.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_exif_rotation/flutter_exif_rotation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';

class GeneralMethods {
  static toNull(_) => null;

  static intToBool(int state) => state == 1;

  static boolToInt(bool state) => state ? 1 : 0;

  static stringToDouble(String? value) =>
      value == null ? null : double.parse(value);

  static doubleToString(double? value) => value?.toString();

  static DateTime getDateTime(String jsonTime, {bool isUtc = false}) {
    final dateFormat = DateFormat(
      jsonTime.contains('T') ? 'yyyy-MM-ddTHH:mm:ss' : 'yyyy-MM-dd HH:mm:ss',
      'en_US',
    );
    return dateFormat.parse(jsonTime, isUtc);
  }

  static Future<File?> pickSingleImage(
    ImageSource imageSource,
    BuildContext context, {
    double aspectRatio = 7 / 5,
  }) async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(
      source: imageSource,
      imageQuality: 70,
    );
    if (image != null) {
      if (!_isImage(image.path)) {
        showErrorMessage(error: 'File is not an image');
      }
      File? file = File(image.path);
      if (file.lengthSync() > (5 * 1000000)) {
        showErrorMessage(error: 'File size should be less than 5MB');
        return null;
      }
      if (file.lengthSync() == 0) {
        showErrorMessage(error: 'Unable to load image');
        return null;
      }
      File rotatedImage = await FlutterExifRotation.rotateImage(
        path: image.path,
      );
      return rotatedImage;
    }
    throw '';
  }

  static bool _isImage(String path) {
    final mimeType = lookupMimeType(path);
    return mimeType?.startsWith('image/') ?? false;
  }

  static String getFileNameFromPath(String name) {
    String filePath = name.substring(name.lastIndexOf('/') + 1);
    filePath = filePath.substring(0, filePath.lastIndexOf('.'));
    return filePath;
  }

  static String getFileExtension(String path) {
    var index = path.lastIndexOf('.');
    if (index < 0 || index + 1 >= path.length) return path;
    return path.substring(index + 1).toLowerCase();
  }

  static get updateSystemColorScheme {
    if (Platform.isAndroid) {
      SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle(
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.dark,
          systemNavigationBarColor: CustomColors.canvas,
          systemNavigationBarIconBrightness: Brightness.dark,
        ),
      );
    } else {
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(statusBarIconBrightness: Brightness.dark),
      );
    }
  }
}
