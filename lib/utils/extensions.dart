import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

extension DurationExt on Duration {
  String get formatDuration {
    if (inMinutes == 0) return '';

    final hours = inHours;
    final minutes = inMinutes.remainder(60);

    if (hours == 0) {
      return '$minutes min';
    } else {
      if (minutes == 0) {
        return '$hours hr';
      } else {
        return '$hours hr $minutes min';
      }
    }
  }
}

extension DateTimeExtension on DateTime {
  String formatDateTime({String? format}) {
    final dateFormat = DateFormat((format ?? 'yyyy-MM-dd HH:mm:ss'), 'en_US');
    return dateFormat.format(this);
  }

  DateTime get toStartOfDay => DateTime(year, month, day);
}

extension TimeOfDayExt on TimeOfDay {
  String get to12HourString {
    final hour = hourOfPeriod;
    final minute = this.minute.toString().padLeft(2, '0');
    final period = this.period.name.toUpperCase();
    return '$hour:$minute $period';
  }

  TimeOfDay addMinutesToTimeOfDay(int minutesToAdd) {
    int totalMinutes = hour * 60 + minute + minutesToAdd;
    totalMinutes = totalMinutes % (24 * 60);
    return TimeOfDay(hour: totalMinutes ~/ 60, minute: totalMinutes % 60);
  }
}

extension DoubleExt on double {
  String get formatPrice {
    if (this % 1 == 0) {
      return NumberFormat('##,##,##,##').format(this); // No decimals
    } else {
      return NumberFormat(
        '##,##,##,##.##',
      ).format(this); // Show 2 decimal places
    }
  }
}

extension IntExt on int {
  String get compactNumber {
    return this > 1500 ? NumberFormat.compact().format(this) : toString();
  }
}
