import '../constants/mock_data.dart';
import '../models/models.dart';
import '../providers/user_provider.dart';

/// Helper class for testing different user verification scenarios
class UserTestHelper {
  
  /// Test with fully verified user (shows appointments)
  static void loginAsVerifiedUser(UserProvider userProvider) {
    userProvider.login(MockData.verifiedUser);
  }
  
  /// Test with partially verified user (email + phone verified, but not identity)
  static void loginAsPartiallyVerifiedUser(UserProvider userProvider) {
    userProvider.login(MockData.partiallyVerifiedUser);
  }
  
  /// Test with unverified user (shows unlock medical records)
  static void loginAsUnverifiedUser(UserProvider userProvider) {
    userProvider.login(MockData.unverifiedUser);
  }
  
  /// Get current user verification status summary
  static String getUserVerificationStatus(UserProvider userProvider) {
    final user = userProvider.currentUser;
    if (user == null) return 'No user logged in';
    
    final emailStatus = user.isEmailVerified ? '✓' : '✗';
    final phoneStatus = user.isPhoneVerified ? '✓' : '✗';
    final identityStatus = user.isUserIdentityVerified ? '✓' : '✗';
    
    return '''
User: ${user.name}
Email Verified: $emailStatus
Phone Verified: $phoneStatus
Identity Verified: $identityStatus
Fully Verified: ${user.isFullyVerified ? '✓' : '✗'}
''';
  }
  
  /// Create a custom user for testing specific scenarios
  static User createCustomTestUser({
    String name = 'Test User',
    String email = '<EMAIL>',
    String phone = '+64 21 000 0000',
    bool isEmailVerified = false,
    bool isPhoneVerified = false,
    bool isUserIdentityVerified = false,
  }) {
    return User(
      id: 'test_user_${DateTime.now().millisecondsSinceEpoch}',
      name: name,
      email: email,
      phone: phone,
      profileImage: 'https://example.com/test-profile.jpg',
      isEmailVerified: isEmailVerified,
      isPhoneVerified: isPhoneVerified,
      isUserIdentityVerified: isUserIdentityVerified,
      createdAt: DateTime.now(),
      lastLoginAt: DateTime.now(),
    );
  }
  
  /// Quick test scenarios
  static void runTestScenario(UserProvider userProvider, TestScenario scenario) {
    switch (scenario) {
      case TestScenario.fullyVerified:
        loginAsVerifiedUser(userProvider);
        break;
      case TestScenario.partiallyVerified:
        loginAsPartiallyVerifiedUser(userProvider);
        break;
      case TestScenario.unverified:
        loginAsUnverifiedUser(userProvider);
        break;
      case TestScenario.emailOnlyVerified:
        final user = createCustomTestUser(
          name: 'Email Only User',
          isEmailVerified: true,
        );
        userProvider.login(user);
        break;
      case TestScenario.phoneOnlyVerified:
        final user = createCustomTestUser(
          name: 'Phone Only User',
          isPhoneVerified: true,
        );
        userProvider.login(user);
        break;
      case TestScenario.identityOnlyVerified:
        final user = createCustomTestUser(
          name: 'Identity Only User',
          isUserIdentityVerified: true,
        );
        userProvider.login(user);
        break;
    }
  }
}

enum TestScenario {
  fullyVerified,
  partiallyVerified,
  unverified,
  emailOnlyVerified,
  phoneOnlyVerified,
  identityOnlyVerified,
}

/// Extension to get description of test scenarios
extension TestScenarioExtension on TestScenario {
  String get description {
    switch (this) {
      case TestScenario.fullyVerified:
        return 'Fully Verified User (Shows Appointments)';
      case TestScenario.partiallyVerified:
        return 'Partially Verified User (Email + Phone)';
      case TestScenario.unverified:
        return 'Unverified User (Shows Unlock Medical Records)';
      case TestScenario.emailOnlyVerified:
        return 'Email Only Verified';
      case TestScenario.phoneOnlyVerified:
        return 'Phone Only Verified';
      case TestScenario.identityOnlyVerified:
        return 'Identity Only Verified';
    }
  }
}
