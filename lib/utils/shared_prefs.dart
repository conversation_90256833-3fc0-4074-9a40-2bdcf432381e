import 'package:shared_preferences/shared_preferences.dart';

import '../constants/data_constants.dart';

const String keySavedUserToken = 'savedUserToken';
const String keyOnBoarding = 'onBoarding';
const String keyUserName = 'userName';
const String keyGridView = 'gridView';

class SharedPrefs {
  Future<void> init() async {
    sharedPreferences = await SharedPreferences.getInstance();
  }

  Future<void> cleanPrefs() async {
    await sharedPreferences.clear();
  }

  String get savedUserToken =>
      sharedPreferences.getString(keySavedUserToken) ?? '';

  void setUserToken(String token) =>
      sharedPreferences.setString(keySavedUserToken, token);

  bool get onboardingComplete =>
      sharedPreferences.getBool(keyOnBoarding) ?? false;

  void get setOnboardingCompleted =>
      sharedPreferences.setBool(keyOnBoarding, true);

  bool get gridView => sharedPreferences.getBool(keyGridView) ?? true;

  set gridView(bool status) => sharedPreferences.setBool(keyGridView, status);
}
