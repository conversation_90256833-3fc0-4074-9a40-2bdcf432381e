import 'dart:io';

import 'package:agnostic_healthcare/common_screens/call_screen.dart';
import 'package:agnostic_healthcare/screens/bottom_navigation_screen/home_tab_screens/sub_screens/wellbeing_detail_screen.dart';
import 'package:agnostic_healthcare/screens/bottom_navigation_screen/providers_tab_screens/sub_screens/my_appointments.dart';
import 'package:flutter/cupertino.dart';

import '../screens/screens.dart';

class Routes {
  static const String splash = '/';

  static const String login = '/login';
  static const String signup = '/signup';
  static const String onboarding = '/onboarding';
  static const String otp = '/otp';
  static const String loginEmailOtp = '/login_phone_otp';
  static const String loginPhoneOtp = '/login_email_otp';
  static const String secureAccount = '/secure_account';
  static const String secureAccountOtp = '/secure_account_otp';

  static const String verifyIdentity = '/verify_identity';
  static const String verifyWithGp = '/verify_with_gp';
  static const String verifyWithHealthCard = '/verify_with_healthcard';
  static const String wellbeingDetail = '/wellbeing_detail';

  static const String bottomNavigation = '/bottom_navigation';

  static const String medications = '/medications';
  static const String immunizations = '/immunizations';

  static const String notifications = '/notifications';
  static const String contactUs = '/contact_us';

  static const String bookAppointment = '/book_appointment';
  static const String bookPaidAppointment = '/book_paid_appointment';

  static const String search = '/search';
  static const String clinicDetail = '/clinic_detail';
  static const String myAppointments = '/my_appointments';
  static const String call = '/call';

  static const String scanDoc = '/scan_doc';
  static const String viewImage = '/view_image';
  static const String viewImageFile = '/view_image_file';
}

class RouteGenerator {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    Widget widget;
    switch (settings.name) {
      case Routes.splash:
        widget = const SplashScreen();
        break;

      case Routes.call:
        widget = const CallScreen();
        break;

      case Routes.login:
        widget = const LoginScreen();
        break;
      case Routes.signup:
        widget = const SignupScreen();
        break;
      case Routes.otp:
        widget = const OtpScreen();
        break;
      case Routes.loginEmailOtp:
        widget = OtpScreen(loginEmail: (settings.arguments as bool?) ?? false);
        break;
      case Routes.loginPhoneOtp:
        widget = OtpScreen(loginPhone: (settings.arguments as bool?) ?? false);
        break;
      case Routes.onboarding:
        widget = const OnboardingScreen();
        break;
      case Routes.secureAccount:
        widget = const SecureAccountScreen();
        break;
      case Routes.secureAccountOtp:
        widget = const SecureAccountOtpScreen();
        break;

      case Routes.wellbeingDetail:
        widget = WellbeingDetailScreen(
          category: (settings.arguments as List<dynamic>)[0],
          title: (settings.arguments as List<dynamic>)[1],
          subtitle: (settings.arguments as List<dynamic>)[2],
        );
        break;

      case Routes.bottomNavigation:
        widget = const BottomNavigationScreen();
        break;

      case Routes.verifyIdentity:
        widget = const VerifyIdentityScreen();
        break;
      case Routes.verifyWithHealthCard:
        widget = const VerifyHealthCardScreen();
        break;
      case Routes.verifyWithGp:
        widget = const VerifyGpDetailsScreen();
        break;

      case Routes.medications:
        widget = const MedicationsScreen();
        break;
      case Routes.immunizations:
        widget = const ImmunizationsScreen();
        break;

      case Routes.scanDoc:
        widget = const ScanDocCamera();
        break;

      case Routes.contactUs:
        widget = const ContactUsScreen();
        break;

      case Routes.bookAppointment:
        widget = const BookAppointmentScreen();
        break;
      case Routes.bookPaidAppointment:
        widget = const BookPaidAppointmentScreen();
        break;

      case Routes.notifications:
        widget = const NotificationsScreen();
        break;

      case Routes.search:
        widget = const SearchScreen();
        break;
      case Routes.clinicDetail:
        widget = const ClinicDetailScreen();
        break;
      case Routes.myAppointments:
        widget = const MyAppointmentsScreen();
        break;

      case Routes.viewImageFile:
        widget = ViewImage(file: settings.arguments as File);
        break;
      case Routes.viewImage:
        widget = ViewImage(url: settings.arguments as String);
        break;

      default:
        widget = const SplashScreen();
        break;
    }
    return CupertinoPageRoute(builder: (context) => widget);
  }
}
