import 'package:flutter/cupertino.dart';
import '../models/models.dart';
import '../constants/mock_data.dart';

class UserProvider with ChangeNotifier {
  User? _currentUser;
  bool _isLoading = false;

  // Getters
  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  bool get isLoggedIn => _currentUser != null;

  // Verification status getters
  bool get isEmailVerified => _currentUser?.isEmailVerified ?? false;
  bool get isPhoneVerified => _currentUser?.isPhoneVerified ?? false;
  bool get isUserIdentityVerified => _currentUser?.isUserIdentityVerified ?? false;
  bool get isFullyVerified => _currentUser?.isFullyVerified ?? false;
  bool get hasAnyVerification => _currentUser?.hasAnyVerification ?? false;

  // Legacy getter for backward compatibility
  bool get identityVerified => isUserIdentityVerified;

  // Login with user data
  void login(User user) {
    _currentUser = user;
    notifyListeners();
  }

  // Login with mock data for testing
  void loginWithMockUser({bool isVerified = true}) {
    _currentUser = MockData.getCurrentMockUser(isVerified: isVerified);
    notifyListeners();
  }

  // Update verification status
  void setVerification(bool status) {
    if (_currentUser != null) {
      _currentUser = _currentUser!.copyWith(isUserIdentityVerified: status);
      notifyListeners();
    }
  }

  // Update email verification
  void setEmailVerification(bool status) {
    if (_currentUser != null) {
      _currentUser = _currentUser!.copyWith(isEmailVerified: status);
      notifyListeners();
    }
  }

  // Update phone verification
  void setPhoneVerification(bool status) {
    if (_currentUser != null) {
      _currentUser = _currentUser!.copyWith(isPhoneVerified: status);
      notifyListeners();
    }
  }

  // Update user profile
  void updateProfile({
    String? name,
    String? email,
    String? phone,
    String? profileImage,
  }) {
    if (_currentUser != null) {
      _currentUser = _currentUser!.copyWith(
        name: name,
        email: email,
        phone: phone,
        profileImage: profileImage,
      );
      notifyListeners();
    }
  }

  // Logout
  void logout() {
    _currentUser = null;
    notifyListeners();
  }

  // Set loading state
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Get user's appointments
  List<Appointment> getUserAppointments() {
    if (_currentUser == null) return [];
    return MockData.getAppointmentsForUser(_currentUser!);
  }

  // Get user's medicine records
  List<MedicineRecord> getUserMedicineRecords() {
    if (_currentUser == null) return [];
    return MockData.getMedicineRecordsForUser(_currentUser!);
  }

  // Get next appointment
  Appointment? getNextAppointment() {
    if (_currentUser == null) return null;
    return MockData.getNextAppointmentForUser(_currentUser!);
  }

  // Get recent medicine record
  MedicineRecord? getRecentMedicineRecord() {
    if (_currentUser == null) return null;
    return MockData.getRecentMedicineRecordForUser(_currentUser!);
  }
}
