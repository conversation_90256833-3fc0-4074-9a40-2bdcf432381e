import 'package:agnostic_healthcare/common_widgets/app_text_field.dart';
import 'package:agnostic_healthcare/common_widgets/round_action_button.dart';
import 'package:agnostic_healthcare/constants/enums.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/utils/routes.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gradient_borders/box_borders/gradient_box_border.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  LoginType loginType = LoginType.email;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: Image.asset('assets/images/splash_bg.png', fit: BoxFit.fill),
        ),
        Scaffold(
          backgroundColor: Colors.transparent,
          body: SingleChildScrollView(
            padding: AppSpacing.pagePaddingLarge,
            child: Column(
              children: [
                AppSpacing.verticalXSpacing(space: kToolbarHeight),
                Image.asset('assets/images/logo.png', height: 65.spMin),
                AppSpacing.verticalXSpacing(space: 47),
                Text(
                  'Sign In',
                  textAlign: TextAlign.center,
                  style: AppTextStyle.styleSansBold(24),
                ),
                AppSpacing.verticalXSpacing(space: 47),
                Container(
                  padding: EdgeInsets.all(5.spMin),
                  width: double.infinity,
                  decoration: BoxDecoration(
                    border: const GradientBoxBorder(
                      gradient: LinearGradient(
                        begin: Alignment.bottomLeft,
                        end: Alignment.topRight,
                        colors: [
                          CustomColors.themeLight,
                          CustomColors.themeWhite,
                        ],
                      ),
                      width: 2,
                    ),
                    borderRadius: AppRoundBorder.circularXpx(),
                  ),
                  child: Row(
                    spacing: 5.spMin,
                    children: [
                      Expanded(
                        child: RoundActionButton(
                          height: 34,
                          fontSize: 13,
                          borderRadius: AppRoundBorder.circularXpx(pixel: 5),
                          elevation: 0,
                          action: () {
                            loginType = LoginType.email;
                            setState(() {});
                          },
                          text: 'Login with Email',
                          showGradiant: loginType == LoginType.email,
                          bgColor:
                              loginType == LoginType.email
                                  ? CustomColors.themeLight
                                  : CustomColors.textWhite,
                          textColor:
                              loginType == LoginType.email
                                  ? CustomColors.textWhite
                                  : CustomColors.textLight,
                        ),
                      ),
                      Expanded(
                        child: RoundActionButton(
                          height: 34,
                          showGradiant: loginType == LoginType.phone,
                          fontSize: 13,
                          borderRadius: AppRoundBorder.circularXpx(pixel: 5),
                          elevation: 0,
                          action: () {
                            loginType = LoginType.phone;
                            setState(() {});
                          },
                          text: 'Login with Number',
                          bgColor:
                              loginType == LoginType.phone
                                  ? CustomColors.themeLight
                                  : CustomColors.textWhite,
                          textColor:
                              loginType == LoginType.phone
                                  ? CustomColors.textWhite
                                  : CustomColors.textLight,
                        ),
                      ),
                    ],
                  ),
                ),
                AppSpacing.verticalXSpacing(space: 32),
                AppTextField(
                  title:
                      loginType == LoginType.phone
                          ? 'Phone Number'
                          : 'Email Address',
                  hintText:
                      loginType == LoginType.phone
                          ? '+64 XX XXX XXXX'
                          : 'Enter Email Address',
                  inputType:
                      loginType == LoginType.phone
                          ? TextInputType.phone
                          : TextInputType.emailAddress,
                  textInputFormatters: [
                    if (loginType == LoginType.phone)
                      MaskTextInputFormatter(
                        mask: '+## ## ### ####',
                        filter: {"#": RegExp(r'[0-9]')},
                        type: MaskAutoCompletionType.lazy,
                      ),
                  ],
                ),
                AppSpacing.verticalXSpacing(space: 32),
                RoundActionButton(
                  text: 'Login',
                  action:
                      () => Navigator.pushNamed(
                        context,
                        loginType == LoginType.email
                            ? Routes.loginEmailOtp
                            : Routes.loginPhoneOtp,
                        arguments: true,
                      ),
                ),
                AppSpacing.verticalXSpacing(space: 18),
                Text.rich(
                  TextSpan(
                    children: [
                      TextSpan(
                        text: 'Don’t have an account? ',
                        style: AppTextStyle.styleSansRegular(14),
                      ),
                      TextSpan(
                        recognizer:
                            TapGestureRecognizer()
                              ..onTap =
                                  () => Navigator.pushNamed(
                                    context,
                                    Routes.signup,
                                  ),
                        text: 'Create Account',
                        style: AppTextStyle.styleSansSemiBold(
                          14,
                          fontColor: CustomColors.theme,
                        ),
                      ),
                    ],
                  ),
                ),
                AppSpacing.verticalXSpacing(space: 32),
                RoundActionButton(
                  textStyle: AppTextStyle.styleSansMedium(
                    16,
                    fontColor: CustomColors.textLight,
                  ),
                  showGradiant: false,
                  bgColor: CustomColors.textWhite,
                  textColor: CustomColors.textGrey,
                  text: 'Login with Google',
                  startIcon: SvgPicture.asset('assets/icons/google.svg'),
                ),
                AppSpacing.verticalXSpacing(space: 15),
                RoundActionButton(
                  action: () => showBiometricBottomSheet,
                  textStyle: AppTextStyle.styleSansMedium(
                    16,
                    fontColor: CustomColors.textLight,
                  ),
                  bgColor: CustomColors.textWhite,
                  showGradiant: false,
                  text: 'Login with Fingerprint',
                  startIcon: SvgPicture.asset('assets/icons/fingerprint.svg'),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void get showBiometricBottomSheet => showModalBottomSheet(
    showDragHandle: true,
    shape: RoundedRectangleBorder(borderRadius: AppRoundBorder.topRound30px),
    context: context,
    backgroundColor: CustomColors.textWhite,
    builder:
        (context) => Container(
          width: double.infinity,
          padding: AppSpacing.pageVerticalPadding,
          child: Column(
            spacing: 5.spMin,
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset('assets/icons/biometric_login.svg'),
              AppSpacing.verticalXSpacing(space: 6),
              Text(
                'Biometric Sign In.',
                style: AppTextStyle.styleSansSemiBold(20),
              ),
              Text(
                'Scan fingerprint to login.',
                style: AppTextStyle.styleSansRegular(16),
              ),
            ],
          ),
        ),
  );
}
