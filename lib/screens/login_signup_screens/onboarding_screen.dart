import 'package:agnostic_healthcare/common_widgets/round_action_button.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/utils/general_methods.dart';
import 'package:agnostic_healthcare/utils/routes.dart';
import 'package:agnostic_healthcare/utils/shared_prefs.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  int currentIndex = 0;
  PageController pageController = PageController();
  late StateSetter dataState;

  @override
  void dispose() {
    GeneralMethods.updateSystemColorScheme;
    super.dispose();
  }

  List<String> get titles => [
    'Welcome to Agnostic',
    'Book Appointments in Minutes',
    'Stay in Control of Your Health',
  ];

  List<String> get details => [
    'Your personal gateway to trusted healthcare and seamless doctor access.',
    'Find and book certified doctors near you—anytime, anywhere.',
    'Access medical records, track, and manage prescriptions easily.',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Positioned.fill(
            child: PageView(
              allowImplicitScrolling: true,
              controller: pageController,
              onPageChanged: (index) {
                currentIndex = index;
                dataState(() {});
              },
              children: [
                Image.asset(
                  'assets/images/onboarding_1.png',
                  fit: BoxFit.cover,
                  filterQuality: FilterQuality.low,
                ),
                Image.asset(
                  'assets/images/onboarding_2.png',
                  filterQuality: FilterQuality.low,
                  fit: BoxFit.cover,
                ),
                Image.asset(
                  'assets/images/onboarding_3.png',
                  filterQuality: FilterQuality.low,
                  fit: BoxFit.cover,
                ),
              ],
            ),
          ),
          StatefulBuilder(
            builder: (context, state) {
              dataState = state;
              return Positioned(
                left: 24.spMin,
                right: 24.spMin,
                bottom: 28.spMin,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  spacing: 10.spMin,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Image.asset('assets/images/logo.png', height: 60.spMin),
                        Row(
                          spacing: 5.spMin,
                          children: List.generate(
                            3,
                            (index) => AnimatedContainer(
                              duration: Duration(milliseconds: 100),
                              curve: Curves.easeIn,
                              height: 7.spMin,
                              width: (index == currentIndex ? 16 : 7).spMin,
                              decoration: BoxDecoration(
                                gradient:
                                    index == currentIndex
                                        ? AppGradients.newKiwiButtonGradiant
                                        : null,
                                color:
                                    index == currentIndex
                                        ? null
                                        : CustomColors.canvas,
                                borderRadius: AppRoundBorder.circularXpx(
                                  pixel: 4,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(),
                    Text(
                      titles[currentIndex],
                      style: AppTextStyle.styleSansBold(
                        20,
                        fontColor: CustomColors.textWhite,
                      ),
                    ),
                    Text(
                      details[currentIndex],
                      style: AppTextStyle.styleSansRegular(
                        17,
                        fontColor: CustomColors.textWhite,
                      ),
                    ),
                    SizedBox(),
                    RoundActionButton(
                      text: 'Next',
                      action: () {
                        if (currentIndex < 2) {
                          pageController.animateToPage(
                            currentIndex + 1,
                            duration: Duration(milliseconds: 500),
                            curve: Curves.easeIn,
                          );
                        } else {
                          SharedPrefs().setOnboardingCompleted;
                          Navigator.pushReplacementNamed(context, Routes.login);
                        }
                      },
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
