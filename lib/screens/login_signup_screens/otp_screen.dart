import 'dart:math';

import 'package:agnostic_healthcare/common_widgets/custom_app_bar.dart';
import 'package:agnostic_healthcare/common_widgets/round_action_button.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:pinput/pinput.dart';

import '../../utils/routes.dart';

class OtpScreen extends StatefulWidget {
  final bool loginEmail;
  final bool loginPhone;

  const OtpScreen({
    super.key,
    this.loginEmail = false,
    this.loginPhone = false,
  });

  @override
  State<OtpScreen> createState() => _OtpScreenState();
}

class _OtpScreenState extends State<OtpScreen> {
  TextEditingController pinController = TextEditingController();
  late PinTheme defaultPinTheme, focusedPinTheme, submittedPinTheme;

  @override
  void initState() {
    super.initState();
    defaultPinTheme = PinTheme(
      width: 56,
      height: 56,
      textStyle: AppTextStyle.styleSansSemiBold(
        28,
        fontColor: CustomColors.textLight,
      ),
      decoration: BoxDecoration(
        color: CustomColors.textWhite,
        borderRadius: AppRoundBorder.circularXpx(pixel: 16),
      ),
    );
    focusedPinTheme = defaultPinTheme.copyDecorationWith(
      border: Border.all(color: CustomColors.theme),
      borderRadius: AppRoundBorder.circularXpx(pixel: 16),
    );

    submittedPinTheme = defaultPinTheme.copyWith(
      decoration: defaultPinTheme.decoration!.copyWith(
        border: Border.all(color: CustomColors.textGrey),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: Image.asset('assets/images/splash_bg.png', fit: BoxFit.fill),
        ),
        Scaffold(
          backgroundColor: Colors.transparent,
          appBar: CustomAppBar(title: ''),
          body: SingleChildScrollView(
            padding: AppSpacing.leftRightSpacingLarge,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              spacing: 16.spMin,
              children: [
                AppSpacing.verticalXSpacing(space: 40),
                Text('Enter Otp', style: AppTextStyle.styleSansBold(24)),
                Text.rich(
                  textAlign: TextAlign.center,
                  TextSpan(
                    children: [
                      TextSpan(
                        text:
                            'We’ve sent an OTP code to your ${widget.loginPhone ? 'phone' : 'email'}, ',
                        style: AppTextStyle.styleSansRegular(
                          16,
                          fontColor: CustomColors.textLight,
                        ),
                      ),
                      TextSpan(
                        text:
                            widget.loginPhone
                                ? '+64 12 345 6789'
                                : '<EMAIL>',
                        style: AppTextStyle.styleSansSemiBold(
                          16,
                          fontColor: CustomColors.theme,
                        ),
                      ),
                    ],
                  ),
                ),
                AppSpacing.verticalXSpacing(space: 24),
                Pinput(
                  preFilledWidget: Text(
                    '0',
                    style: AppTextStyle.styleSansSemiBold(
                      28,
                      fontColor: CustomColors.textLight,
                    ),
                  ),
                  controller: pinController,
                  length: 6,
                  defaultPinTheme: defaultPinTheme,
                  focusedPinTheme: focusedPinTheme,
                  submittedPinTheme: submittedPinTheme,
                  keyboardType: TextInputType.number,

                  pinputAutovalidateMode: PinputAutovalidateMode.onSubmit,
                  showCursor: true,
                  onCompleted: (pin) {},
                  separatorBuilder:
                      (index) => AppSpacing.horizontalXSpacing(space: 16),
                ),
                SizedBox(),
                Text.rich(
                  textAlign: TextAlign.center,
                  TextSpan(
                    children: [
                      TextSpan(
                        text: 'We will resend the code in ',
                        style: AppTextStyle.styleSansRegular(
                          16,
                          fontColor: CustomColors.textLight,
                        ),
                      ),
                      TextSpan(
                        text: '59 s\n',
                        style: AppTextStyle.styleSansSemiBold(
                          16,
                          fontColor: CustomColors.theme,
                        ),
                      ),
                      TextSpan(
                        text: 'Resend Now',
                        style: AppTextStyle.styleSansSemiBold(
                          16,
                          fontColor: CustomColors.theme,
                          textDecoration: TextDecoration.underline,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(),
                RoundActionButton(
                  text: 'Verify',
                  action: () {
                    if (widget.loginPhone || widget.loginEmail) {
                      Navigator.pushNamedAndRemoveUntil(
                        context,
                        Routes.bottomNavigation,
                        (_) => false,
                      );
                    } else {
                      showEmailVerifyBottomSheet(Random().nextBool());
                    }
                  },
                ),
                SizedBox(),
                if (!widget.loginPhone && !widget.loginEmail)
                  Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                          text: 'Already have an account? ',
                          style: AppTextStyle.styleSansRegular(14),
                        ),
                        TextSpan(
                          recognizer:
                              TapGestureRecognizer()
                                ..onTap = () => Navigator.pop(context),
                          text: 'Sign In',
                          style: AppTextStyle.styleSansSemiBold(
                            14,
                            fontColor: CustomColors.theme,
                          ),
                        ),
                      ],
                    ),
                  ),
                AppSpacing.verticalXSpacing(space: 32),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void showEmailVerifyBottomSheet(bool confirmed) => showModalBottomSheet(
    isDismissible: false,
    showDragHandle: true,
    shape: RoundedRectangleBorder(borderRadius: AppRoundBorder.topRound30px),
    context: context,
    backgroundColor: CustomColors.textWhite,
    builder:
        (context) => Container(
          width: double.infinity,
          padding: AppSpacing.leftRightSpacingLarge,
          child: Column(
            spacing: 5.spMin,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                spacing: 27.spMin,
                children: [
                  SvgPicture.asset(
                    'assets/icons/${confirmed ? 'email_confirm' : 'email_confirm_failed'}.svg',
                  ),
                  Column(
                    spacing: 5.spMin,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        confirmed ? 'Email Confirmed' : 'Confirmation Failed',
                        style: AppTextStyle.styleSansSemiBold(20),
                      ),
                      Text(
                        'Kindly confirm your information.',
                        style: AppTextStyle.styleSansRegular(
                          16,
                          fontColor: CustomColors.textLight,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              AppSpacing.verticalXSpacing(space: 6),
              RoundActionButton(
                action: () {
                  if (confirmed) {
                    Navigator.pushNamedAndRemoveUntil(
                      context,
                      Routes.secureAccount,
                      (_) => false,
                    );
                  } else {
                    Navigator.pop(context);
                  }
                },
                showGradiant: confirmed,
                bgColor: confirmed ? null : CustomColors.greyBackground,
                text: confirmed ? 'Secure Your Account' : 'Retry',
              ),
              if (confirmed)
                TextButton(
                  onPressed:
                      () =>
                          Navigator.pushNamed(context, Routes.bottomNavigation),
                  child: Text(
                    'Go to Home',
                    style: AppTextStyle.styleSansRegular(
                      14,
                      fontColor: CustomColors.textLight,
                    ),
                  ),
                ),
            ],
          ),
        ),
  );
}
