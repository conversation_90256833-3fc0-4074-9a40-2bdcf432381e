import 'package:agnostic_healthcare/common_widgets/app_text_field.dart';
import 'package:agnostic_healthcare/common_widgets/custom_app_bar.dart';
import 'package:agnostic_healthcare/common_widgets/loading_dialog.dart';
import 'package:agnostic_healthcare/common_widgets/round_action_button.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/utils/routes.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SignupScreen extends StatefulWidget {
  const SignupScreen({super.key});

  @override
  State<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends State<SignupScreen> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: Image.asset('assets/images/splash_bg.png', fit: BoxFit.fill),
        ),
        Scaffold(
          backgroundColor: Colors.transparent,
          appBar: CustomAppBar(title: ''),
          body: SingleChildScrollView(
            padding: AppSpacing.leftRightSpacingLarge,
            child: Column(
              spacing: 16.spMin,
              children: [
                Text(
                  'Create an Account',
                  style: AppTextStyle.styleSansBold(24),
                ),
                AppSpacing.verticalXSpacing(space: 41),
                AppTextField(title: 'First Name'),
                AppTextField(title: 'Last Name'),
                AppTextField(title: 'Email Address'),
                SizedBox(),
                RoundActionButton(
                  text: 'Create Account',
                  action: () {
                    showDialog(
                      context: context,
                      barrierDismissible: false,
                      // Prevents closing the dialog by tapping outside
                      builder: (BuildContext context) {
                        return const LoadingDialog(
                          message: 'Creating Account...',
                        );
                      },
                    );
                    Future.delayed(Duration(seconds: 2), () {
                      Navigator.pushReplacementNamed(context, Routes.otp);
                    });
                  },
                ),
                Text(
                  'By creating an account, you agree to our Terms of Service and Privacy Policy.',
                  style: AppTextStyle.styleSansRegular(
                    10,
                    fontColor: CustomColors.textLight,
                  ),
                ),
                Text.rich(
                  TextSpan(
                    children: [
                      TextSpan(
                        text: 'Already have an account? ',
                        style: AppTextStyle.styleSansRegular(14),
                      ),
                      TextSpan(
                        recognizer:
                            TapGestureRecognizer()
                              ..onTap = () => Navigator.pop(context),
                        text: 'Sign In',
                        style: AppTextStyle.styleSansSemiBold(
                          14,
                          fontColor: CustomColors.theme,
                        ),
                      ),
                    ],
                  ),
                ),
                AppSpacing.verticalXSpacing(space: 32),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
