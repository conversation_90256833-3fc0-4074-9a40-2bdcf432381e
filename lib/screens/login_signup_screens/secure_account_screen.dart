import 'package:agnostic_healthcare/common_widgets/app_text_field.dart';
import 'package:agnostic_healthcare/common_widgets/custom_app_bar.dart';
import 'package:agnostic_healthcare/common_widgets/icon_button.dart';
import 'package:agnostic_healthcare/common_widgets/round_action_button.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/utils/routes.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

class SecureAccountScreen extends StatefulWidget {
  const SecureAccountScreen({super.key});

  @override
  State<SecureAccountScreen> createState() => _SecureAccountScreenState();
}

class _SecureAccountScreenState extends State<SecureAccountScreen> {
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Stack(
        children: [
          Positioned.fill(
            child: Image.asset('assets/images/splash_bg.png', fit: BoxFit.fill),
          ),
          Scaffold(
            backgroundColor: Colors.transparent,
            appBar: CustomAppBar(
              title: '',
              leading: Center(
                child: AppIconButton(
                  bgColor: Colors.transparent,
                  action:
                      () =>
                          Navigator.pushNamed(context, Routes.bottomNavigation),
                  icon: SvgPicture.asset('assets/icons/back.svg'),
                  padding: 0.spMin,
                  iconAlignment: Alignment.centerLeft,
                  size: 50,
                ),
              ),
            ),
            body: SingleChildScrollView(
              padding: AppSpacing.leftRightSpacingLarge,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                spacing: 14.spMin,
                children: [
                  Text(
                    'Secure Your Account',
                    style: AppTextStyle.styleSansBold(24),
                  ),
                  SizedBox(),
                  Text(
                    'Please enter your mobile number. We\'ll send you a code to make sure it\'s you.',
                    textAlign: TextAlign.center,
                    style: AppTextStyle.styleSansRegular(
                      16,
                      fontColor: CustomColors.textLight,
                    ),
                  ),
                  AppSpacing.verticalXSpacing(space: 84),
                  AppTextField(
                    leadingWidget: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 21.spMin),
                      child: Row(
                        spacing: 10.spMin,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'NZ',
                            style: AppTextStyle.styleSansSemiBold(
                              18,
                              fontColor: CustomColors.theme,
                            ),
                          ),
                          SvgPicture.asset('assets/icons/arrow_down.svg'),
                        ],
                      ),
                    ),
                    style: AppTextStyle.styleSansSemiBold(16),
                    hintText: 'xx-xxx-xxxx',
                    hintStyle: AppTextStyle.styleSansMedium(
                      16,
                      letterSpacing: 5,
                      fontColor: CustomColors.textLight,
                    ),
                    textInputFormatters: [
                      MaskTextInputFormatter(
                        mask: '##-###-####',
                        filter: {"#": RegExp(r'[0-9]')},
                        type: MaskAutoCompletionType.lazy,
                      ),
                    ],
                    inputType: TextInputType.number,
                  ),
                  SizedBox(),
                  RoundActionButton(
                    text: 'Send Code',
                    action:
                        () => Navigator.pushNamed(
                          context,
                          Routes.secureAccountOtp,
                        ),
                  ),
                  SizedBox(),
                  AppSpacing.verticalXSpacing(space: 32),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
