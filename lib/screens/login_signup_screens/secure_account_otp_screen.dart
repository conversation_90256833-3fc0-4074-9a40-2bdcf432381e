import 'package:agnostic_healthcare/common_widgets/custom_app_bar.dart';
import 'package:agnostic_healthcare/common_widgets/round_action_button.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:pinput/pinput.dart';

import '../../utils/routes.dart';

class SecureAccountOtpScreen extends StatefulWidget {
  const SecureAccountOtpScreen({super.key});

  @override
  State<SecureAccountOtpScreen> createState() => _SecureAccountOtpScreenState();
}

class _SecureAccountOtpScreenState extends State<SecureAccountOtpScreen> {
  TextEditingController pinController = TextEditingController();
  late PinTheme defaultPinTheme, focusedPinTheme, submittedPinTheme;

  @override
  void initState() {
    super.initState();
    defaultPinTheme = PinTheme(
      width: 56,
      height: 56,
      textStyle: AppTextStyle.styleSansSemiBold(
        28,
        fontColor: CustomColors.textLight,
      ),
      decoration: BoxDecoration(
        color: CustomColors.textWhite,
        borderRadius: AppRoundBorder.circularXpx(pixel: 16),
      ),
    );
    focusedPinTheme = defaultPinTheme.copyDecorationWith(
      border: Border.all(color: CustomColors.theme),
      borderRadius: AppRoundBorder.circularXpx(pixel: 16),
    );

    submittedPinTheme = defaultPinTheme.copyWith(
      decoration: defaultPinTheme.decoration!.copyWith(
        border: Border.all(color: CustomColors.textLight),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: Image.asset('assets/images/splash_bg.png', fit: BoxFit.fill),
        ),
        Scaffold(
          backgroundColor: Colors.transparent,
          appBar: CustomAppBar(title: ''),
          body: SingleChildScrollView(
            padding: AppSpacing.leftRightSpacingLarge,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              spacing: 16.spMin,
              children: [
                AppSpacing.verticalXSpacing(space: 40),
                Text(
                  'Enter the 6-Digit Code',
                  style: AppTextStyle.styleSansBold(24),
                ),
                Text.rich(
                  textAlign: TextAlign.center,
                  TextSpan(
                    children: [
                      TextSpan(
                        text:
                            'We’ve sent a 6-Digit code to your Phone Number, ',
                        style: AppTextStyle.styleSansRegular(
                          16,
                          fontColor: CustomColors.textLight,
                        ),
                      ),
                      TextSpan(
                        text: '+64 23 456 9876',
                        style: AppTextStyle.styleSansSemiBold(
                          16,
                          fontColor: CustomColors.theme,
                        ),
                      ),
                    ],
                  ),
                ),
                AppSpacing.verticalXSpacing(space: 24),
                Pinput(
                  preFilledWidget: Text(
                    '0',
                    style: AppTextStyle.styleSansSemiBold(
                      28,
                      fontColor: CustomColors.textLight,
                    ),
                  ),
                  controller: pinController,
                  length: 7,
                  defaultPinTheme: defaultPinTheme,
                  focusedPinTheme: focusedPinTheme,
                  submittedPinTheme: submittedPinTheme,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    MaskTextInputFormatter(
                      mask: '###-###',
                      filter: {"#": RegExp(r'[0-9]')},
                      type: MaskAutoCompletionType.lazy,
                    ),
                  ],
                  pinputAutovalidateMode: PinputAutovalidateMode.onSubmit,
                  showCursor: true,
                  onCompleted: (pin) {},
                  separatorBuilder:
                      (index) => AppSpacing.horizontalXSpacing(space: 7),
                ),
                SizedBox(),
                Text.rich(
                  textAlign: TextAlign.center,
                  TextSpan(
                    children: [
                      TextSpan(
                        text: 'We will resend the code in ',
                        style: AppTextStyle.styleSansRegular(
                          16,
                          fontColor: CustomColors.textLight,
                        ),
                      ),
                      TextSpan(
                        text: '59 s\n',
                        style: AppTextStyle.styleSansSemiBold(
                          16,
                          fontColor: CustomColors.theme,
                        ),
                      ),
                      TextSpan(
                        text: 'Resend Now',
                        style: AppTextStyle.styleSansSemiBold(
                          16,
                          fontColor: CustomColors.theme,
                          textDecoration: TextDecoration.underline,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(),
                RoundActionButton(
                  text: 'Verify',
                  action: () => showAccVerifyBottomSheet,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void get showAccVerifyBottomSheet => showModalBottomSheet(
    isDismissible: false,
    showDragHandle: true,
    shape: RoundedRectangleBorder(borderRadius: AppRoundBorder.topRound30px),
    context: context,
    backgroundColor: CustomColors.textWhite,
    builder:
        (context) => Container(
          width: double.infinity,
          padding: AppSpacing.leftRightSpacingLarge,
          child: Column(
            spacing: 5.spMin,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                spacing: 27.spMin,
                children: [
                  Image.asset('assets/icons/acc_verified.png'),
                  Column(
                    spacing: 5.spMin,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Account Verified',
                        style: AppTextStyle.styleSansSemiBold(20),
                      ),
                      Text(
                        'Phone Number is Confirmed.',
                        style: AppTextStyle.styleSansRegular(
                          16,
                          fontColor: CustomColors.textLight,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              AppSpacing.verticalXSpacing(space: 6),
              RoundActionButton(
                action: () {
                  Navigator.pushNamedAndRemoveUntil(
                    context,
                    Routes.bottomNavigation,
                    (_) => false,
                  );
                },
                text: 'Done',
              ),
            ],
          ),
        ),
  );
}
