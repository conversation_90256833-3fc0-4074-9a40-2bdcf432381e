import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/providers/user_provider.dart';
import 'package:agnostic_healthcare/utils/general_methods.dart';
import 'package:agnostic_healthcare/utils/shared_prefs.dart';
import 'package:agnostic_healthcare/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  late UserProvider userProvider;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    userProvider = Provider.of<UserProvider>(context);
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (SharedPrefs().onboardingComplete) {
        // if (SharedPrefs().savedUserToken.isNotEmpty) {
        //   BagTalkDio().getProfile(
        //     onSuccess: (user) {
        //       userProvider.login(user);
        //       Navigator.pushReplacementNamed(
        //         navigatorKey.currentContext!,
        //         user.isCompleted
        //             ? Routes.bottomNavigation
        //             : Routes.profileSetup,
        //       );
        //     },
        //     onError: (error) {
        //       showErrorMessage(error: error);
        //       Navigator.pushReplacementNamed(context, Routes.profileSetup);
        //     },
        //   );
        // } else {
        await Future.delayed(Duration(seconds: 3));
        GeneralMethods.updateSystemColorScheme;
        Navigator.pushReplacementNamed(context, Routes.login);
        // }
      } else {
        await Future.delayed(Duration(seconds: 3));
        Navigator.pushReplacementNamed(context, Routes.onboarding);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: AppGradients.splashBackgroundGradient,
        ),
        child: Stack(
          children: [
            Positioned.fill(
              child: Image.asset(
                'assets/images/splash_bg.png',
                fit: BoxFit.cover,
              ),
            ),
            Center(
              child: Image.asset('assets/images/logo.png', width: 200.spMin),
            ),
          ],
        ),
      ),
    );
  }
}
