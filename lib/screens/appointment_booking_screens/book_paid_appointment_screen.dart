import 'dart:math';

import 'package:agnostic_healthcare/common_widgets/app_card.dart';
import 'package:agnostic_healthcare/common_widgets/custom_app_bar.dart';
import 'package:agnostic_healthcare/common_widgets/profile_image_widget.dart';
import 'package:agnostic_healthcare/common_widgets/round_action_button.dart';
import 'package:agnostic_healthcare/constants/data_constants.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/screens/appointment_booking_screens/sub_widgets/app_and_patient_type_screen.dart';
import 'package:agnostic_healthcare/screens/appointment_booking_screens/sub_widgets/app_booking_summary_screen.dart';
import 'package:agnostic_healthcare/screens/appointment_booking_screens/sub_widgets/app_payment_screen.dart';
import 'package:agnostic_healthcare/screens/appointment_booking_screens/sub_widgets/app_reason_screen.dart';
import 'package:agnostic_healthcare/screens/appointment_booking_screens/sub_widgets/app_time_selection_screen.dart';
import 'package:expandable_page_view/expandable_page_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BookPaidAppointmentScreen extends StatefulWidget {
  const BookPaidAppointmentScreen({super.key});

  @override
  State<BookPaidAppointmentScreen> createState() =>
      _BookPaidAppointmentScreenState();
}

class _BookPaidAppointmentScreenState extends State<BookPaidAppointmentScreen> {
  int totalPages = 5;
  int currentPage = 0;
  bool personalApp = true;
  bool isOnlineConsultation = true;
  PageController pageController = PageController();
  late StateSetter progressState;
  String? service;
  ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (canPop, _) {
        if (currentPage == 0) {
          Navigator.pop(context);
        } else {
          pageController.jumpToPage(currentPage - 1);
        }
      },
      child: Stack(
        children: [
          Positioned.fill(
            child: Image.asset('assets/images/splash_bg.png', fit: BoxFit.fill),
          ),
          Scaffold(
            backgroundColor: Colors.transparent,
            appBar: CustomAppBar(
              hideBackButton: true,
              title: '',
              centerTitle: false,
              titleWidget: Expanded(
                child: Row(
                  spacing: 4.spMin,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Expanded(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Book an Appointment',
                            style: AppTextStyle.styleSansSemiBold(20),
                          ),
                          Text(
                            'Book an appointment with a GP.',
                            style: AppTextStyle.styleSansRegular(
                              12,
                              fontColor: CustomColors.textLight,
                            ),
                          ),
                        ],
                      ),
                    ),
                    ProfileImageWidget(imageSize: 56, imageUrl: profile),
                  ],
                ),
              ),
            ),
            body: SingleChildScrollView(
              controller: _scrollController,
              padding: AppSpacing.pagePaddingSmall,
              child: Column(
                spacing: 12.spMin,
                children: [
                  AppCard(
                    bgColor: CustomColors.textWhite,
                    child: Column(
                      spacing: 10.spMin,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Book an Appointment',
                              style: AppTextStyle.styleSansSemiBold(14),
                            ),
                            InkWell(
                              onTap: () async {
                                if (currentPage == 0) {
                                  Navigator.pop(context);
                                } else {
                                  await pageController.previousPage(
                                    duration: Duration(milliseconds: 500),
                                    curve: Curves.easeIn,
                                  );
                                  _scrollController.animateTo(
                                    0,
                                    duration: Duration(milliseconds: 500),
                                    curve: Curves.easeIn,
                                  );
                                }
                              },
                              child: Text(
                                'Back',
                                style: AppTextStyle.styleSansBold(
                                  14,
                                  fontColor: CustomColors.themeLight,
                                ),
                              ),
                            ),
                          ],
                        ),
                        StatefulBuilder(
                          builder: (context, state) {
                            progressState = state;
                            return LinearProgressIndicator(
                              minHeight: 5.spMin,
                              borderRadius: AppRoundBorder.circularXpx(),
                              value: (currentPage + 1) / totalPages,
                              color: CustomColors.theme,
                              backgroundColor: CustomColors.textSecondaryLight,
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                  ExpandablePageView(
                    controller: pageController,
                    onPageChanged: (index) {
                      currentPage = index;
                      progressState(() {});
                    },
                    physics: NeverScrollableScrollPhysics(),
                    children: [
                      AppAndPatientTypeScreen(
                        isPaid: true,
                        changePatientType:
                            (patientType) => personalApp = patientType,
                        changeConsultationType:
                            (consultationType) =>
                                isOnlineConsultation = consultationType,
                        personalApp: personalApp,
                        isOnlineConsultation: isOnlineConsultation,
                      ),
                      AppTimeSelectionScreen(isPaid: true),
                      AppReasonScreen(isPaid: true),
                      AppPaymentScreen(),
                      AppBookingSummaryScreen(),
                    ],
                  ),
                  AppSpacing.verticalXSpacing(),
                  RoundActionButton(
                    text: currentPage == 3 ? 'Pay \$70' : 'Next',
                    action: () async {
                      if (currentPage == 3) {
                        showPaymentDialog(Random().nextBool());
                      } else if (currentPage == 4) {
                        showConfirmDialog;
                      } else {
                        await pageController.nextPage(
                          duration: Duration(milliseconds: 100),
                          curve: Curves.easeIn,
                        );
                        _scrollController.animateTo(
                          0,
                          duration: Duration(milliseconds: 500),
                          curve: Curves.easeIn,
                        );
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void showPaymentDialog(bool isSuccess) => showModalBottomSheet(
    isDismissible: false,
    showDragHandle: true,
    enableDrag: false,
    shape: RoundedRectangleBorder(borderRadius: AppRoundBorder.topRound30px),
    context: context,
    backgroundColor: CustomColors.textWhite,
    builder:
        (context) => PopScope(
          canPop: false,
          child: Container(
            width: double.infinity,
            padding: AppSpacing.leftRightSpacingLarge,
            child: Column(
              spacing: 5.spMin,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  spacing: 27.spMin,
                  children: [
                    Image.asset(
                      'assets/icons/${isSuccess ? 'pay_success' : 'pay_failed'}.png',
                    ),
                    Expanded(
                      child: Column(
                        spacing: 5.spMin,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            isSuccess
                                ? 'Payment Successful'
                                : 'Payment Failed.',
                            style: AppTextStyle.styleSansSemiBold(20),
                          ),
                          Text(
                            isSuccess
                                ? 'Check summary and confirm booking.'
                                : 'Check summary and confirm booking.',
                            style: AppTextStyle.styleSansRegular(
                              16,
                              fontColor: CustomColors.textLight,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                AppSpacing.verticalXSpacing(space: 6),
                RoundActionButton(
                  action: () {
                    if (isSuccess) {
                      Navigator.pop(context);
                      _scrollController.animateTo(
                        0,
                        duration: Duration(milliseconds: 500),
                        curve: Curves.easeIn,
                      );

                      pageController.nextPage(
                        duration: Duration(milliseconds: 500),
                        curve: Curves.easeIn,
                      );
                    } else {
                      Navigator.pop(context);
                    }
                  },
                  text: isSuccess ? 'Continue' : 'Retry',
                  showGradiant: isSuccess,
                  bgColor: isSuccess ? null : CustomColors.textSecondaryLight,
                ),
              ],
            ),
          ),
        ),
  );

  void get showConfirmDialog => showModalBottomSheet(
    isDismissible: false,
    showDragHandle: true,
    enableDrag: false,
    shape: RoundedRectangleBorder(borderRadius: AppRoundBorder.topRound30px),
    context: context,
    backgroundColor: CustomColors.textWhite,
    builder:
        (context) => PopScope(
          canPop: false,
          child: Container(
            width: double.infinity,
            padding: AppSpacing.leftRightSpacingLarge,
            child: Column(
              spacing: 5.spMin,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  spacing: 27.spMin,
                  children: [
                    Image.asset('assets/icons/verify_success.png'),
                    Expanded(
                      child: Column(
                        spacing: 5.spMin,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Booking Successful',
                            style: AppTextStyle.styleSansSemiBold(20),
                          ),
                          Text(
                            'Your appointment with Dr. Eren is confirmed for September 30, 2025, at 10:00 AM.',
                            style: AppTextStyle.styleSansRegular(
                              14,
                              fontColor: CustomColors.textLight,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                AppSpacing.verticalXSpacing(space: 6),
                RoundActionButton(
                  action:
                      () =>
                          Navigator.popUntil(context, (route) => route.isFirst),
                  text: 'Go to Home',
                ),
              ],
            ),
          ),
        ),
  );
}
