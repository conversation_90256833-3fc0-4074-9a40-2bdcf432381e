import 'package:agnostic_healthcare/common_widgets/app_text_field.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/screens/bottom_navigation_screen/my_gp_tab_screens/sub_widgets/appointment_card.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class AppBookingSummaryScreen extends StatefulWidget {
  const AppBookingSummaryScreen({super.key});

  @override
  State<AppBookingSummaryScreen> createState() =>
      _AppBookingSummaryScreenState();
}

class _AppBookingSummaryScreenState extends State<AppBookingSummaryScreen> {
  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 12.spMin,
      children: [
        AppSpacing.verticalXSpacing(space: 7),
        Text('Confirm your Booking', style: AppTextStyle.styleSansSemiBold(16)),
        SizedBox(),
        AppointmentCard(),
        AppTextField(
          title: 'Practitioner',
          titleStyle: AppTextStyle.styleSansMedium(
            12,
            fontColor: CustomColors.textLight,
          ),
          trailingWidget: SvgPicture.asset('assets/icons/arrow_down_dark.svg'),
          isReadOnly: true,
          leadingWidget: SvgPicture.asset(
            'assets/icons/you.svg',
            color: CustomColors.themeLight,
            height: 24.spMin,
          ),
          textController: TextEditingController(text: 'Dr Eren Wesley'),
        ),
        AppTextField(
          title: 'Clinic',
          titleStyle: AppTextStyle.styleSansMedium(
            12,
            fontColor: CustomColors.textLight,
          ),
          trailingWidget: SvgPicture.asset('assets/icons/arrow_down_dark.svg'),
          isReadOnly: true,
          leadingWidget: SvgPicture.asset(
            'assets/icons/center.svg',
            height: 24.spMin,
          ),
          textController: TextEditingController(text: 'Holloway Health Center'),
        ),
        AppTextField(
          title: 'Address',
          titleStyle: AppTextStyle.styleSansMedium(
            12,
            fontColor: CustomColors.textLight,
          ),
          isReadOnly: true,
          leadingWidget: SvgPicture.asset('assets/icons/hospital_location.svg'),
          textController: TextEditingController(
            text: 'Delta Avenue, Auckland, NZ',
          ),
        ),

        AppTextField(
          title: 'Contact Number',
          titleStyle: AppTextStyle.styleSansMedium(
            12,
            fontColor: CustomColors.textLight,
          ),
          isReadOnly: true,
          leadingWidget: SvgPicture.asset('assets/icons/phone.svg'),
          textController: TextEditingController(text: '+64 23 124 2715'),
        ),
        AppTextField(
          title: 'Date',
          titleStyle: AppTextStyle.styleSansMedium(
            12,
            fontColor: CustomColors.textLight,
          ),
          isReadOnly: true,
          leadingWidget: SvgPicture.asset('assets/icons/date.svg'),
          textController: TextEditingController(text: 'September 30, 2025'),
          trailingWidget: SvgPicture.asset('assets/icons/arrow_down_dark.svg'),
        ),
        AppTextField(
          title: 'Time',
          titleStyle: AppTextStyle.styleSansMedium(
            12,
            fontColor: CustomColors.textLight,
          ),
          isReadOnly: true,
          leadingWidget: SvgPicture.asset('assets/icons/schedule.svg'),
          textController: TextEditingController(text: '10:00 AM - 11:00 AM'),
        ),
        Text(
          'By confirming the booking, you agree to our Terms of Service and Privacy Policy.',
          style: AppTextStyle.styleSansMedium(
            12,
            fontColor: CustomColors.textLight,
          ),
        ),
      ],
    );
  }
}
