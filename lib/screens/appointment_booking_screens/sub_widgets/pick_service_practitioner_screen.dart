import 'package:agnostic_healthcare/common_widgets/app_card.dart';
import 'package:agnostic_healthcare/common_widgets/custom_dropdown.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class PickServicePractitionerScreen extends StatefulWidget {
  final String? currentService;
  final Function(String) onServiceSelected;

  const PickServicePractitionerScreen({
    super.key,
    required this.onServiceSelected,
    this.currentService,
  });

  @override
  State<PickServicePractitionerScreen> createState() =>
      _PickServicePractitionerScreenState();
}

class _PickServicePractitionerScreenState
    extends State<PickServicePractitionerScreen> {
  String? service;

  @override
  void initState() {
    super.initState();
    service = widget.currentService;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 12.spMin,
      children: [
        AppSpacing.verticalXSpacing(space: 7),
        Text('Pick a Service', style: AppTextStyle.styleSansSemiBold(16)),
        SizedBox(),
        CustomDropdown(
          icon: Padding(
            padding: EdgeInsets.only(right: 10.spMin),
            child: SvgPicture.asset('assets/icons/service.svg'),
          ),
          options: ['Dermatologist'],
          hint: 'Select Service',
          onSelect: (selection) {
            service = selection;
            widget.onServiceSelected(selection);
          },
          selection: service,
        ),
        AppSpacing.verticalXSpacing(space: 7),
        Text(
          'Select a Practitioner/Clinic',
          style: AppTextStyle.styleSansSemiBold(16),
        ),
        SizedBox(),
        clinicSelectionTile(
          iconName: 'profile',
          type: 'Your GP',
          title: 'Dr Eren Wesley',
          detail: 'Holloway Health Center',
          isSelected: specificClinic,
          action: () {
            specificClinic = true;
            setState(() {});
          },
        ),
        clinicSelectionTile(
          iconName: 'someone_circled',
          type: 'Your Practice',
          title: 'Anyone Available',
          detail: 'Holloway Health Center',
          isSelected: !specificClinic,
          action: () {
            specificClinic = false;
            setState(() {});
          },
        ),

        if (!specificClinic) ...[
          AppSpacing.verticalXSpacing(space: 7),
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              'All Practitioners',
              style: AppTextStyle.styleSansSemiBold(16),
            ),
          ),
          AppCard(
            bgColor: CustomColors.textWhite,
            child: Column(
              spacing: 8.spMin,
              children: [
                Row(
                  spacing: 15.spMin,
                  children: [
                    SvgPicture.asset('assets/icons/center.svg'),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Holloway Health Center',
                          style: AppTextStyle.styleSansSemiBold(18),
                        ),
                        Text(
                          '37 Practitioners',
                          style: AppTextStyle.styleSansMedium(
                            14,
                            fontColor: CustomColors.textLight,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                Row(
                  children: [
                    SvgPicture.asset('assets/icons/location.svg'),
                    Text(
                      '190M, Auckland, NZ',
                      style: AppTextStyle.styleSansMedium(
                        14,
                        fontColor: CustomColors.textLight,
                      ),
                    ),
                  ],
                ),
                ...List.generate(10, (i) => doctorCard),
              ],
            ),
          ),
          SizedBox(),
        ],
      ],
    );
  }

  Widget get doctorCard => Padding(
    padding: EdgeInsets.symmetric(vertical: 4.spMin),
    child: Row(
      spacing: 10.spMin,
      children: [
        Container(
          height: 37.spMin,
          width: 38.spMin,
          decoration: BoxDecoration(
            borderRadius: AppRoundBorder.circularXpx(pixel: 5),
            color: true ? CustomColors.themeLight : CustomColors.textLight,
          ),
          child: Center(child: SvgPicture.asset('assets/icons/you.svg')),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Dr James', style: AppTextStyle.styleSansSemiBold(16)),
            Text(
              'Male GP',
              style: AppTextStyle.styleSansMedium(
                12,
                fontColor: CustomColors.textLight,
              ),
            ),
          ],
        ),
      ],
    ),
  );

  bool specificClinic = false;

  Widget clinicSelectionTile({
    required String iconName,
    required String type,
    required String title,
    required String detail,
    required bool isSelected,
    Function()? action,
  }) => AppCard(
    border: Border.all(
      color: isSelected ? CustomColors.themeLight : CustomColors.textWhite,
      width: 2,
    ),
    action: action,
    bgColor: CustomColors.textWhite,
    child: Row(
      spacing: 10.spMin,
      children: [
        Container(
          height: 53.spMin,
          width: 53.spMin,
          decoration: BoxDecoration(
            borderRadius: AppRoundBorder.circularXpx(pixel: 5),
            color:
                isSelected ? CustomColors.themeLight : CustomColors.textLight,
          ),
          child: Center(child: SvgPicture.asset('assets/icons/$iconName.svg')),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 3.spMin,
          children: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: 6.spMin),
              decoration: BoxDecoration(
                color:
                    isSelected
                        ? CustomColors.themeLight
                        : CustomColors.textLight,
                borderRadius: AppRoundBorder.circularXpx(pixel: 12),
              ),
              child: Text(
                type,
                style: AppTextStyle.styleSansBold(
                  8,
                  fontColor: CustomColors.textWhite,
                ),
              ),
            ),
            Text(title, style: AppTextStyle.styleSansBold(14)),
            Text(
              detail,
              style: AppTextStyle.styleSansRegular(
                12,
                fontColor: CustomColors.textLight,
              ),
            ),
          ],
        ),
      ],
    ),
  );
}
