import 'package:agnostic_healthcare/common_widgets/app_card.dart';
import 'package:agnostic_healthcare/common_widgets/app_text_field.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/screens/bottom_navigation_screen/providers_tab_screens/sub_widgets/clinic_card.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class AppReasonScreen extends StatefulWidget {
  final bool isPaid;

  const AppReasonScreen({super.key, this.isPaid = false});

  @override
  State<AppReasonScreen> createState() => _AppReasonScreenState();
}

class _AppReasonScreenState extends State<AppReasonScreen> {
  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 12.spMin,
      children: [
        if (widget.isPaid)
          ClinicCard(showBookmarkIcon: false)
        else
        AppCard(
          bgColor: CustomColors.textWhite,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 5.spMin,
            children: [
              Text(
                'Dr Eren Wesley | Holloway Health Center',
                style: AppTextStyle.styleSansSemiBold(14),
              ),
              Row(
                children: [
                  Text(
                    'Dermatologist ',
                    style: AppTextStyle.styleSansMedium(14),
                  ),
                  AppSpacing.horizontalXSpacing(),
                  SvgPicture.asset('assets/icons/location.svg'),
                  Text(
                    '190M, Auckland, NZ',
                    style: AppTextStyle.styleSansMedium(
                      14,
                      fontColor: CustomColors.textLight,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        AppSpacing.verticalXSpacing(space: 7),
        Text(
          'Reason for Appointment',
          style: AppTextStyle.styleSansSemiBold(16),
        ),
        SizedBox(),
        Text(
          'Share all the symptoms with your practitioner, or any info the reception may need to know.',
          style: AppTextStyle.styleSansMedium(
            12,
            fontColor: CustomColors.textLight,
          ),
        ),
        AppTextField(
          maxLines: 5,
          hintText: 'Reason for Appointment (optional)',
        ),
      ],
    );
  }
}
