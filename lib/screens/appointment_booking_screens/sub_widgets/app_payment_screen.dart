import 'package:agnostic_healthcare/common_widgets/app_text_field.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/screens/bottom_navigation_screen/providers_tab_screens/sub_widgets/clinic_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

class AppPaymentScreen extends StatefulWidget {
  const AppPaymentScreen({super.key});

  @override
  State<AppPaymentScreen> createState() => _AppPaymentScreenState();
}

class _AppPaymentScreenState extends State<AppPaymentScreen> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 12.spMin,
      children: [
        ClinicCard(showBookmarkIcon: false),
        AppSpacing.verticalXSpacing(space: 7),
        Center(
          child: Text('Payment', style: AppTextStyle.styleSansSemiBold(16)),
        ),
        SizedBox(),
        Text('Online Consultation', style: AppTextStyle.styleSansSemiBold(16)),
        Text(
          '\$70.00',
          style: AppTextStyle.styleSansBold(18, fontColor: CustomColors.theme),
        ),
        SizedBox(),
        Text('Select Card', style: AppTextStyle.styleSansSemiBold(16)),
        Row(
          spacing: 20.spMin,
          children: [
            Row(
              spacing: 5.spMin,
              children: [
                Icon(
                  Icons.radio_button_off_outlined,
                  color: CustomColors.themeLight,
                ),
                SvgPicture.asset('assets/icons/mastercard.svg'),
              ],
            ),
            Row(
              spacing: 5.spMin,
              children: [
                Icon(
                  Icons.radio_button_off_outlined,
                  color: CustomColors.themeLight,
                ),
                SvgPicture.asset('assets/icons/visa_card.svg'),
              ],
            ),
            Row(
              spacing: 5.spMin,
              children: [
                Icon(
                  Icons.radio_button_off_outlined,
                  color: CustomColors.themeLight,
                ),
                SvgPicture.asset('assets/icons/mnp_card.svg'),
              ],
            ),
          ],
        ),
        SizedBox(),
        AppTextField(
          title: 'Card Number',
          titleStyle: AppTextStyle.styleSansSemiBold(16),
          inputType: TextInputType.number,
          hintText: '1234 5678 9101 1121',
          textInputFormatters: [
            MaskTextInputFormatter(
              mask: '#### #### #### ####',
              filter: {"#": RegExp(r'[0-9]')},
              type: MaskAutoCompletionType.lazy,
            ),
          ],
        ),
        SizedBox(),
        Row(
          spacing: 15.spMin,
          children: [
            Expanded(
              child: AppTextField(
                title: 'Exp Date',
                titleStyle: AppTextStyle.styleSansSemiBold(16),
                isReadOnly: true,
                hintText: 'MM/YY',
                onTap: () async {
                  DateTime? pickedDate = await showDatePicker(
                    context: context,
                    firstDate: DateTime(1940),
                    lastDate: DateTime.now(),
                  );
                  if (pickedDate != null) {}
                },
              ),
            ),
            Expanded(
              child: AppTextField(
                hintText: '123',
                title: 'CVV',
                maxLength: 3,
                textInputFormatters: [
                  MaskTextInputFormatter(
                    mask: '###',
                    filter: {"#": RegExp(r'[0-9]')},
                    type: MaskAutoCompletionType.lazy,
                  ),
                ],
                inputType: TextInputType.number,
                titleStyle: AppTextStyle.styleSansSemiBold(16),
              ),
            ),
          ],
        ),
        SizedBox(),
        Row(
          spacing: 15.spMin,
          children: [
            Icon(
              Icons.check_box_outline_blank_outlined,
              color: CustomColors.themeLight,
            ),
            Text(
              'Save card details',
              style: AppTextStyle.styleSansRegular(
                14,
                fontColor: CustomColors.textLight,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
