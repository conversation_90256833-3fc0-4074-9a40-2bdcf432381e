import 'package:agnostic_healthcare/common_widgets/app_card.dart';
import 'package:agnostic_healthcare/common_widgets/app_text_field.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/screens/bottom_navigation_screen/providers_tab_screens/sub_widgets/practitioner_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

class AppAndPatientTypeScreen extends StatefulWidget {
  final bool isPaid;
  final Function(bool) changePatientType;
  final Function(bool) changeConsultationType;
  final bool personalApp;
  final bool isOnlineConsultation;

  const AppAndPatientTypeScreen({
    super.key,
    required this.changePatientType,
    required this.changeConsultationType,
    required this.personalApp,
    required this.isOnlineConsultation,
    this.isPaid = false,
  });

  @override
  State<AppAndPatientTypeScreen> createState() =>
      _AppAndPatientTypeScreenState();
}

class _AppAndPatientTypeScreenState extends State<AppAndPatientTypeScreen> {
  @override
  void initState() {
    super.initState();
    personalApp = widget.personalApp;
    isOnlineConsultation = widget.isOnlineConsultation;
  }

  bool personalApp = true;
  bool isOnlineConsultation = true;

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 12.spMin,
      children: [
        if (widget.isPaid) ...[
          AppSpacing.verticalXSpacing(space: 7),
          Text(
            'Who is this Appointment For?',
            style: AppTextStyle.styleSansSemiBold(16),
          ),
          SizedBox(
            height: 170.spMin,
            child: ListView.separated(
              separatorBuilder:
                  (context, i) => AppSpacing.horizontalXSpacing(space: 12),
              scrollDirection: Axis.horizontal,
              padding: EdgeInsets.symmetric(
                vertical: 10.spMin,
                horizontal: 2.spMin,
              ),
              itemCount: 5,
              shrinkWrap: true,
              itemBuilder:
                  (context, index) =>
                      PractitionerCard(showBookingButton: false),
            ),
          ),
        ],
        Text(
          'Who is this Appointment For?',
          style: AppTextStyle.styleSansSemiBold(16),
        ),
        SizedBox(),
        selectionTile(
          iconName: 'you',
          title: 'You',
          isSelected: personalApp,
          action: () {
            personalApp = true;
            widget.changePatientType(personalApp);
            setState(() {});
          },
        ),
        selectionTile(
          iconName: 'someone',
          title: 'Someone Else',
          isSelected: !personalApp,
          action: () {
            personalApp = false;
            widget.changePatientType(personalApp);
            setState(() {});
          },
        ),
        if (!personalApp) ...[
          AppTextField(title: 'Patient Name', hintText: 'Enter full name'),
          AppTextField(
            title: 'Phone Number',
            hintText: '+64 XX XXX XXXX',
            inputType: TextInputType.number,
            textInputFormatters: [
              MaskTextInputFormatter(
                mask: '+## ## ### ####',
                filter: {"#": RegExp(r'[0-9]')},
                type: MaskAutoCompletionType.lazy,
              ),
            ],
          ),
          AppTextField(
            title: 'Date of Birth',
            hintText: 'Enter full name',
            isReadOnly: true,
            onTap: () async {
              DateTime? pickedDate = await showDatePicker(
                context: context,
                firstDate: DateTime(1940),
                lastDate: DateTime.now(),
              );
              if (pickedDate != null) {}
            },
          ),
        ],
        AppSpacing.verticalXSpacing(space: 7),
        Text(
          'Select type of Appointment',
          style: AppTextStyle.styleSansSemiBold(16),
        ),
        SizedBox(),
        selectionTile(
          iconName: 'online',
          title: 'Online Consultation',
          isSelected: isOnlineConsultation,
          action: () {
            isOnlineConsultation = true;
            widget.changeConsultationType(isOnlineConsultation);
            setState(() {});
          },
        ),
        selectionTile(
          iconName: 'someone',
          title: 'In-Person Consultation',
          isSelected: !isOnlineConsultation,
          action: () {
            isOnlineConsultation = false;
            widget.changeConsultationType(isOnlineConsultation);
            setState(() {});
          },
        ),
      ],
    );
  }

  Widget selectionTile({
    required String iconName,
    required String title,
    required bool isSelected,
    Function()? action,
  }) => AppCard(
    border: Border.all(
      color: isSelected ? CustomColors.themeLight : CustomColors.textWhite,
      width: 2,
    ),
    action: action,
    bgColor: CustomColors.textWhite,
    child: Row(
      spacing: 10.spMin,
      children: [
        Container(
          height: 37.spMin,
          width: 38.spMin,
          decoration: BoxDecoration(
            borderRadius: AppRoundBorder.circularXpx(pixel: 5),
            color:
                isSelected ? CustomColors.themeLight : CustomColors.textLight,
          ),
          child: Center(child: SvgPicture.asset('assets/icons/$iconName.svg')),
        ),
        Text(
          title,
          style: AppTextStyle.styleSansSemiBold(
            16,
            fontColor:
                isSelected ? CustomColors.themeLight : CustomColors.textLight,
          ),
        ),
      ],
    ),
  );
}
