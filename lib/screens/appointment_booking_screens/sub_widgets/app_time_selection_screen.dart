import 'package:agnostic_healthcare/common_widgets/app_card.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/screens/bottom_navigation_screen/providers_tab_screens/sub_widgets/clinic_card.dart';
import 'package:agnostic_healthcare/utils/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';

class AppTimeSelectionScreen extends StatefulWidget {
  final bool isPaid;
  const AppTimeSelectionScreen({super.key, this.isPaid = false});

  @override
  State<AppTimeSelectionScreen> createState() => _AppTimeSelectionScreenState();
}

class _AppTimeSelectionScreenState extends State<AppTimeSelectionScreen> {
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;

  @override
  void initState() {
    super.initState();
    _selectedDay = _focusedDay;
  }

  final List<DateTime> unavailableDays = [
    DateTime.utc(2025, 9, 13), // Friday
    DateTime.utc(2025, 9, 14), // Saturday
    DateTime.utc(2025, 9, 21), // Sunday
    DateTime.utc(2025, 9, 27), // Saturday
    DateTime.utc(2025, 9, 28), // Sunday
  ];

  bool _isDayUnavailable(DateTime day) {
    return unavailableDays.any(
      (unavailableDay) => isSameDay(unavailableDay, day),
    );
  }

  List<DateTime> get slots => List.generate(
    9,
    (index) => DateTime.now()
        .copyWith(hour: 9, minute: 0)
        .add(Duration(minutes: index * 30)),
  );
  DateTime? selectedSlot;

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 12.spMin,
      children: [
        if (widget.isPaid)
          ClinicCard(showBookmarkIcon: false)
        else
          AppCard(
            bgColor: CustomColors.textWhite,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 5.spMin,
              children: [
                Text(
                  'Dr Eren Wesley | Holloway Health Center',
                  style: AppTextStyle.styleSansSemiBold(14),
                ),
                Row(
                  children: [
                    Text(
                      'Dermatologist ',
                      style: AppTextStyle.styleSansMedium(14),
                    ),
                    AppSpacing.horizontalXSpacing(),
                    SvgPicture.asset('assets/icons/location.svg'),
                    Text(
                      '190M, Auckland, NZ',
                      style: AppTextStyle.styleSansMedium(
                        14,
                        fontColor: CustomColors.textLight,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        AppSpacing.verticalXSpacing(space: 7),
        Text('Select Date and Time', style: AppTextStyle.styleSansSemiBold(16)),
        SizedBox(),
        AppCard(
          bgColor: CustomColors.textWhite,
          child: TableCalendar(
            firstDay: DateTime.utc(2020, 1, 1),
            lastDay: DateTime.utc(2030, 12, 31),
            focusedDay: _focusedDay,
            selectedDayPredicate: (day) {
              return isSameDay(_selectedDay, day);
            },
            onDaySelected: (selectedDay, focusedDay) {
              setState(() {
                _selectedDay = selectedDay;
                _focusedDay = focusedDay;
              });
            },
            enabledDayPredicate: (day) {
              return !_isDayUnavailable(day);
            },
            onPageChanged: (focusedDay) {
              _focusedDay = focusedDay;
            },
            calendarFormat: CalendarFormat.month,
            headerStyle: HeaderStyle(
              formatButtonVisible: false,
              leftChevronVisible: true,
              rightChevronVisible: true,
              titleTextFormatter:
                  (date, locale) => DateFormat('MMMM yyyy').format(date),
              titleCentered: true,
              rightChevronIcon: Icon(
                Icons.arrow_right,
                color: CustomColors.text,
              ),
              leftChevronIcon: Icon(Icons.arrow_left, color: CustomColors.text),
              titleTextStyle: AppTextStyle.styleSansBold(14),
            ),
            daysOfWeekStyle: DaysOfWeekStyle(
              weekendStyle: AppTextStyle.styleSansBold(
                12,
                fontColor: CustomColors.themeLight,
              ),
              weekdayStyle: AppTextStyle.styleSansBold(
                12,
                fontColor: CustomColors.themeLight,
              ),
            ),
            calendarStyle: CalendarStyle(
              todayDecoration: BoxDecoration(
                color: CustomColors.themeLight,
                borderRadius: AppRoundBorder.circularXpx(pixel: 8),
              ),
              selectedDecoration: BoxDecoration(
                color: CustomColors.themeLight,
                borderRadius: AppRoundBorder.circularXpx(pixel: 8),
              ),
              selectedTextStyle: AppTextStyle.styleSansBold(
                12,
                fontColor: CustomColors.textWhite,
              ),
              todayTextStyle: AppTextStyle.styleSansBold(12),
              defaultTextStyle: AppTextStyle.styleSansBold(
                12,
                fontColor: CustomColors.themeLight,
              ),
              weekendTextStyle: AppTextStyle.styleSansBold(
                12,
                fontColor: CustomColors.themeLight,
              ),
              disabledTextStyle: AppTextStyle.styleSansBold(
                12,
                fontColor: CustomColors.textSecondaryLight,
              ),
              outsideDaysVisible: false,
            ),
          ),
        ),
        AppSpacing.verticalXSpacing(space: 7),
        Text(
          '10:00 AM | 30 September 2025',
          style: AppTextStyle.styleSansSemiBold(
            18,
            fontColor: CustomColors.themeLight,
          ),
        ),
        Text(
          'All times are New Zealand Standard Time (NZST)',
          textAlign: TextAlign.center,
          style: AppTextStyle.styleSansMedium(
            12,
            fontColor: CustomColors.textLight,
          ),
        ),
        GridView.builder(
          physics: NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            mainAxisSpacing: 16.spMin,
            crossAxisSpacing: 10.spMin,
            childAspectRatio: 2.5,
          ),
          shrinkWrap: true,
          itemCount: slots.length,
          itemBuilder: (context, index) {
            bool isSelected =
                slots[index].hour == selectedSlot?.hour &&
                slots[index].minute == selectedSlot?.minute;
            return AppCard(
              borderRadius: 8,
              action: () {
                selectedSlot = slots[index];
                setState(() {});
              },
              height: 41.spMin,
              bgColor:
                  isSelected ? CustomColors.themeLight : CustomColors.textWhite,
              child: Center(
                child: Text(
                  slots[index].formatDateTime(format: 'h:mm aa'),
                  style: AppTextStyle.styleSansBold(
                    14,
                    fontColor:
                        isSelected
                            ? CustomColors.textWhite
                            : CustomColors.textLight,
                  ),
                ),
              ),
            );
          },
        ),
        Text(
          'If times slots that you want are not available select a different practive.',
          style: AppTextStyle.styleSansMedium(
            12,
            fontColor: CustomColors.textLight,
          ),
        ),
      ],
    );
  }
}
