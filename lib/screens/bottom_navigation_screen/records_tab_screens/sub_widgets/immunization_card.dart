import 'package:agnostic_healthcare/common_widgets/app_card.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ImmunizationCard extends StatelessWidget {
  const ImmunizationCard({super.key});

  @override
  Widget build(BuildContext context) {
    return AppCard(
      bgColor: CustomColors.textWhite,
      child: Column(
        spacing: 3.spMin,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 6.spMin),
            decoration: BoxDecoration(
              color: CustomColors.themeLight,
              borderRadius: AppRoundBorder.circularXpx(pixel: 12),
            ),
            child: Text(
              'Immunisation',
              style: AppTextStyle.styleSansMedium(
                8,
                fontColor: CustomColors.textWhite,
              ),
            ),
          ),
          Text(
            'Phizer Tetanus Shot',
            style: AppTextStyle.styleSansSemiBold(16),
          ),
          Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: 'Prescribed by Dr <PERSON> | ',
                  style: AppTextStyle.styleSansRegular(12),
                ),
                TextSpan(
                  text: '10 August 2025',
                  style: AppTextStyle.styleSansMedium(
                    12,
                    fontColor: CustomColors.themeLight,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 15.spMin,
            children: [
              Text(
                'Protects Against:',
                style: AppTextStyle.styleSansRegular(11),
              ),
              Expanded(
                child: Text(
                  'Diphtheria, Tetanus, Whooping Cough',
                  style: AppTextStyle.styleSansRegular(
                    11,
                    fontColor: CustomColors.textLight,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
