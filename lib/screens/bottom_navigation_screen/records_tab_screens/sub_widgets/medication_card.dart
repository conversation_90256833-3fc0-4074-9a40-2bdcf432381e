import 'package:agnostic_healthcare/common_widgets/app_card.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class MedicationCard extends StatelessWidget {
  const MedicationCard({super.key});

  @override
  Widget build(BuildContext context) {
    return AppCard(
      bgColor: CustomColors.textWhite,
      child: Column(
        spacing: 3.spMin,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 6.spMin),
            decoration: BoxDecoration(
              color: CustomColors.themeLight,
              borderRadius: AppRoundBorder.circularXpx(pixel: 12),
            ),
            child: Text(
              'Medication',
              style: AppTextStyle.styleSansMedium(
                8,
                fontColor: CustomColors.textWhite,
              ),
            ),
          ),
          Text(
            'Paracetamol Extra  |  30 MG',
            style: AppTextStyle.styleSansSemiBold(16),
          ),
          Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: 'Prescribed by <PERSON> <PERSON> | ',
                  style: AppTextStyle.styleSansRegular(12),
                ),
                TextSpan(
                  text: '10 August 2025',
                  style: AppTextStyle.styleSansMedium(
                    12,
                    fontColor: CustomColors.themeLight,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 15.spMin,
            children: [
              Text('Instructions:', style: AppTextStyle.styleSansRegular(11)),
              Expanded(
                child: Text(
                  'Take the pills with Luke warm water.',
                  style: AppTextStyle.styleSansRegular(
                    11,
                    fontColor: CustomColors.textLight,
                  ),
                ),
              ),
            ],
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 15.spMin,
            children: [
              Text('Reason:', style: AppTextStyle.styleSansRegular(11)),
              Expanded(
                child: Text(
                  'Severe Headaches and body pain.',
                  style: AppTextStyle.styleSansRegular(
                    11,
                    fontColor: CustomColors.textLight,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
