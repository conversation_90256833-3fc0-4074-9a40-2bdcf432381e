import 'package:agnostic_healthcare/common_widgets/app_card.dart';
import 'package:agnostic_healthcare/common_widgets/custom_app_bar.dart';
import 'package:agnostic_healthcare/common_widgets/profile_image_widget.dart';
import 'package:agnostic_healthcare/constants/data_constants.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/screens/bottom_navigation_screen/records_tab_screens/sub_widgets/immunization_card.dart';
import 'package:agnostic_healthcare/screens/bottom_navigation_screen/records_tab_screens/sub_widgets/medication_card.dart';
import 'package:agnostic_healthcare/utils/routes.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class RecordsTab extends StatefulWidget {
  const RecordsTab({super.key});

  @override
  State<RecordsTab> createState() => _RecordsTabState();
}

class _RecordsTabState extends State<RecordsTab> {
  bool medicineCardOpened = false;
  bool immunizationCardOpened = false;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: Image.asset('assets/images/splash_bg.png', fit: BoxFit.fill),
        ),
        Scaffold(
          appBar: CustomAppBar(
            title: '',
            centerTitle: false,
            titleWidget: Expanded(
              child: Row(
                spacing: 4.spMin,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  SvgPicture.asset('assets/icons/record.svg', height: 50.spMin),
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'My Records',
                          style: AppTextStyle.styleSansSemiBold(20),
                        ),
                        Text(
                          'Your Medical and Test Records.',
                          style: AppTextStyle.styleSansRegular(
                            12,
                            fontColor: CustomColors.textLight,
                          ),
                        ),
                      ],
                    ),
                  ),
                  ProfileImageWidget(imageSize: 56, imageUrl: profile),
                ],
              ),
            ),
          ),
          backgroundColor: Colors.transparent,
          body: SingleChildScrollView(
            padding: AppSpacing.pagePaddingLarge,
            child: Column(
              spacing: 15.spMin,
              children: [
                AppCard(
                  bgColor:
                      medicineCardOpened
                          ? CustomColors.themeCardBgLight
                          : CustomColors.textWhite,
                  child: Column(
                    spacing: 12.spMin,
                    children: [
                      Row(
                        spacing: 10.spMin,
                        children: [
                          if (!medicineCardOpened)
                            Image.asset('assets/icons/medicine.png'),
                          Text(
                            'Medications',
                            style: AppTextStyle.styleSansBold(18),
                          ),
                          Spacer(),
                          TextButton(
                            onPressed: () {
                              medicineCardOpened = !medicineCardOpened;
                              setState(() {});
                            },
                            child: Text(
                              medicineCardOpened ? 'Close' : 'Open',
                              style: AppTextStyle.styleSansSemiBold(
                                14,
                                fontColor: CustomColors.themeLight,
                              ),
                            ),
                          ),
                        ],
                      ),
                      if (medicineCardOpened) ...[
                        MedicationCard(),
                        MedicationCard(),
                        TextButton(
                          onPressed:
                              () => Navigator.pushNamed(
                                context,
                                Routes.medications,
                              ),
                          child: Text(
                            'See All',
                            style: AppTextStyle.styleSansSemiBold(
                              14,
                              fontColor: CustomColors.themeLight,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                AppCard(
                  bgColor:
                      immunizationCardOpened
                          ? CustomColors.themeCardBgLight
                          : CustomColors.textWhite,
                  child: Column(
                    spacing: 12.spMin,
                    children: [
                      Row(
                        spacing: 10.spMin,
                        children: [
                          if (!immunizationCardOpened)
                            Image.asset('assets/icons/immunization.png'),
                          Text(
                            'Immunisations',
                            style: AppTextStyle.styleSansBold(18),
                          ),
                          Spacer(),
                          TextButton(
                            onPressed: () {
                              immunizationCardOpened = !immunizationCardOpened;
                              setState(() {});
                            },
                            child: Text(
                              immunizationCardOpened ? 'Close' : 'Open',
                              style: AppTextStyle.styleSansSemiBold(
                                14,
                                fontColor: CustomColors.themeLight,
                              ),
                            ),
                          ),
                        ],
                      ),
                      if (immunizationCardOpened) ...[
                        ImmunizationCard(),
                        ImmunizationCard(),
                        TextButton(
                          onPressed:
                              () => Navigator.pushNamed(
                                context,
                                Routes.immunizations,
                              ),
                          child: Text(
                            'See All',
                            style: AppTextStyle.styleSansSemiBold(
                              14,
                              fontColor: CustomColors.themeLight,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
