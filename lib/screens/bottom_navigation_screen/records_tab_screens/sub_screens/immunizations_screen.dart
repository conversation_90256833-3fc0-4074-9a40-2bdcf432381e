import 'package:agnostic_healthcare/common_widgets/custom_app_bar.dart';
import 'package:agnostic_healthcare/common_widgets/profile_image_widget.dart';
import 'package:agnostic_healthcare/common_widgets/round_action_button.dart';
import 'package:agnostic_healthcare/constants/data_constants.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/screens/bottom_navigation_screen/records_tab_screens/sub_widgets/immunization_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gradient_borders/box_borders/gradient_box_border.dart';

class ImmunizationsScreen extends StatefulWidget {
  const ImmunizationsScreen({super.key});

  @override
  State<ImmunizationsScreen> createState() => _ImmunizationsScreenState();
}

class _ImmunizationsScreenState extends State<ImmunizationsScreen> {
  int currentIndex = 0;
  PageController pageController = PageController();

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: Image.asset('assets/images/splash_bg.png', fit: BoxFit.fill),
        ),
        Scaffold(
          backgroundColor: Colors.transparent,
          appBar: CustomAppBar(
            title: '',
            centerTitle: false,
            titleWidget: Expanded(
              child: Row(
                spacing: 4.spMin,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Immunisations',
                          style: AppTextStyle.styleSansSemiBold(20),
                        ),
                        Text(
                          'Your Immunisations details',
                          style: AppTextStyle.styleSansRegular(
                            12,
                            fontColor: CustomColors.textLight,
                          ),
                        ),
                      ],
                    ),
                  ),
                  ProfileImageWidget(imageSize: 56, imageUrl: profile),
                ],
              ),
            ),
          ),
          body: Padding(
            padding: AppSpacing.pagePaddingSmall,
            child: Column(
              spacing: 15.spMin,
              children: [
                StatefulBuilder(
                  builder: (context, state) {
                    return Container(
                      padding: EdgeInsets.all(5.spMin),
                      width: double.infinity,
                      decoration: BoxDecoration(
                        border: const GradientBoxBorder(
                          gradient: LinearGradient(
                            begin: Alignment.bottomLeft,
                            end: Alignment.topRight,
                            colors: [
                              CustomColors.themeLight,
                              CustomColors.themeWhite,
                            ],
                          ),
                          width: 2,
                        ),
                        borderRadius: AppRoundBorder.circularXpx(),
                      ),
                      child: Row(
                        spacing: 5.spMin,
                        children: [
                          Expanded(
                            child: RoundActionButton(
                              height: 34,
                              fontSize: 14,
                              borderRadius: AppRoundBorder.circularXpx(
                                pixel: 5,
                              ),
                              elevation: 0,
                              action: () {
                                currentIndex = 0;
                                state(() {});
                                pageController.animateToPage(
                                  0,
                                  duration: Duration(milliseconds: 500),
                                  curve: Curves.easeIn,
                                );
                              },
                              text: 'Current',
                              showGradiant: currentIndex == 0,
                              bgColor:
                                  currentIndex == 0
                                      ? CustomColors.themeLight
                                      : CustomColors.textWhite,
                              textColor:
                                  currentIndex == 0
                                      ? CustomColors.textWhite
                                      : CustomColors.textLight,
                            ),
                          ),
                          Expanded(
                            child: RoundActionButton(
                              height: 34,
                              showGradiant: currentIndex == 1,
                              fontSize: 14,
                              borderRadius: AppRoundBorder.circularXpx(
                                pixel: 5,
                              ),
                              elevation: 0,
                              action: () {
                                currentIndex = 1;
                                state(() {});
                                pageController.animateToPage(
                                  1,
                                  duration: Duration(milliseconds: 500),
                                  curve: Curves.easeIn,
                                );
                              },
                              text: 'Past',
                              bgColor:
                                  currentIndex == 1
                                      ? CustomColors.themeLight
                                      : CustomColors.textWhite,
                              textColor:
                                  currentIndex == 1
                                      ? CustomColors.textWhite
                                      : CustomColors.textLight,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
                Expanded(
                  child: PageView(
                    physics: NeverScrollableScrollPhysics(),
                    controller: pageController,
                    children: [currentMedicinesPage, pastMedicinePage],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget get currentMedicinesPage => ListView.separated(
    itemCount: 5,
    separatorBuilder: (context, i) => AppSpacing.verticalXSpacing(space: 15),
    itemBuilder: (context, index) => ImmunizationCard(),
  );

  Widget get pastMedicinePage => ListView.separated(
    itemCount: 5,
    separatorBuilder: (context, i) => AppSpacing.verticalXSpacing(space: 15),
    itemBuilder: (context, index) => ImmunizationCard(),
  );
}
