import 'package:agnostic_healthcare/common_widgets/app_text_field.dart';
import 'package:agnostic_healthcare/common_widgets/custom_app_bar.dart';
import 'package:agnostic_healthcare/common_widgets/round_action_button.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ContactUsScreen extends StatelessWidget {
  const ContactUsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: Image.asset('assets/images/splash_bg.png', fit: BoxFit.fill),
        ),
        Scaffold(
          backgroundColor: Colors.transparent,
          appBar: CustomAppBar(
            title: '',
            centerTitle: false,
            titleWidget: Expanded(
              child: Row(
                spacing: 4.spMin,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Contact & Support',
                        style: AppTextStyle.styleSansSemiBold(20),
                      ),
                      Text(
                        'Contact us for support. we will reply via email.',
                        style: AppTextStyle.styleSansRegular(
                          12,
                          fontColor: CustomColors.textLight,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          body: SingleChildScrollView(
            padding: AppSpacing.pagePaddingLarge,
            child: Column(
              spacing: 15.spMin,
              children: [
                AppTextField(hintText: 'Name'),
                AppTextField(hintText: 'Email Address'),
                AppTextField(hintText: 'Type your message', maxLines: 5),
                AppSpacing.verticalXSpacing(),
                RoundActionButton(text: 'Send'),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
