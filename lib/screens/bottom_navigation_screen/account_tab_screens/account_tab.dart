import 'package:agnostic_healthcare/common_widgets/app_card.dart';
import 'package:agnostic_healthcare/common_widgets/app_text_field.dart';
import 'package:agnostic_healthcare/common_widgets/custom_app_bar.dart';
import 'package:agnostic_healthcare/common_widgets/profile_image_widget.dart';
import 'package:agnostic_healthcare/common_widgets/round_action_button.dart';
import 'package:agnostic_healthcare/constants/data_constants.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/utils/routes.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:pinput/pinput.dart';

class AccountTab extends StatefulWidget {
  const AccountTab({super.key});

  @override
  State<AccountTab> createState() => _AccountTabState();
}

class _AccountTabState extends State<AccountTab> {
  late PinTheme defaultPinTheme, focusedPinTheme, submittedPinTheme;

  @override
  void initState() {
    super.initState();
    defaultPinTheme = PinTheme(
      width: 56,
      height: 56,
      textStyle: AppTextStyle.styleSansSemiBold(
        28,
        fontColor: CustomColors.textLight,
      ),
      decoration: BoxDecoration(
        color: CustomColors.textWhite,
        borderRadius: AppRoundBorder.circularXpx(pixel: 16),
      ),
    );
    focusedPinTheme = defaultPinTheme.copyDecorationWith(
      border: Border.all(color: CustomColors.theme),
      borderRadius: AppRoundBorder.circularXpx(pixel: 16),
    );

    submittedPinTheme = defaultPinTheme.copyWith(
      decoration: defaultPinTheme.decoration!.copyWith(
        border: Border.all(color: CustomColors.textLight),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: Image.asset('assets/images/splash_bg.png', fit: BoxFit.fill),
        ),
        Scaffold(
          backgroundColor: Colors.transparent,
          appBar: CustomAppBar(
            title: '',
            centerTitle: false,
            titleWidget: Expanded(
              child: Row(
                spacing: 4.spMin,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  SvgPicture.asset(
                    'assets/icons/account.svg',
                    height: 50.spMin,
                  ),
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Account',
                          style: AppTextStyle.styleSansSemiBold(20),
                        ),
                        Text(
                          'Your Profile and App Settings.',
                          style: AppTextStyle.styleSansRegular(
                            12,
                            fontColor: CustomColors.textLight,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          body: SingleChildScrollView(
            padding: AppSpacing.pagePaddingSmall,
            child: Column(
              spacing: 15.spMin,
              children: [
                AppCard(
                  bgColor: CustomColors.textWhite,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Personal Details',
                        style: AppTextStyle.styleSansSemiBold(16),
                      ),
                      Text(
                        'This information is linked to your official identity and cannot be changed in the app. If these details are incorrect, please contact support.',
                        style: AppTextStyle.styleSansRegular(
                          10,
                          fontColor: CustomColors.textLight,
                        ),
                      ),
                      AppSpacing.verticalXSpacing(),
                      AppCard(
                        elevation: 2,
                        bgColor: CustomColors.textWhite,
                        child: Row(
                          spacing: 15.spMin,
                          children: [
                            ProfileImageWidget(
                              imageSize: 68,
                              imageUrl: profile,
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              spacing: 3.spMin,
                              children: [
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 5.spMin,
                                  ),
                                  decoration: BoxDecoration(
                                    color: CustomColors.themeLight,
                                    borderRadius: AppRoundBorder.circularXpx(
                                      pixel: 12,
                                    ),
                                  ),
                                  child: Text(
                                    'Verified',
                                    style: AppTextStyle.styleSansMedium(
                                      10,
                                      fontColor: CustomColors.textWhite,
                                    ),
                                  ),
                                ),
                                SizedBox(),
                                Text(
                                  'Andrew Smith',
                                  style: AppTextStyle.styleSansSemiBold(16),
                                ),
                                Text.rich(
                                  TextSpan(
                                    children: [
                                      TextSpan(
                                        text: 'DOB: ',
                                        style: AppTextStyle.styleSansSemiBold(
                                          10,
                                        ),
                                      ),
                                      TextSpan(
                                        text: '12 JAN 1999  ',
                                        style: AppTextStyle.styleSansMedium(
                                          10,
                                          fontColor: CustomColors.textLight,
                                        ),
                                      ),
                                      TextSpan(
                                        text: 'NHI: ',
                                        style: AppTextStyle.styleSansSemiBold(
                                          10,
                                        ),
                                      ),
                                      TextSpan(
                                        text: 'A3BCHD12',
                                        style: AppTextStyle.styleSansMedium(
                                          10,
                                          fontColor: CustomColors.textLight,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                AppCard(
                  bgColor: CustomColors.textWhite,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Account & Security',
                        style: AppTextStyle.styleSansSemiBold(16),
                      ),
                      Text(
                        'Editable contact and login information.',
                        style: AppTextStyle.styleSansRegular(10),
                      ),
                      infoWidget(
                        title: 'Contact Number',
                        detail: '+64 32 234 1235',
                      ),
                      infoWidget(
                        title: 'Email Address',
                        detail: '<EMAIL>',
                        action: () => changeEmailBottomSheet,
                      ),
                      infoWidget(
                        title: 'Home Address',
                        detail: '2, 6th Avenue, Parks Lane, Auckland, NZ',
                      ),
                      infoWidget(title: 'Password', detail: '**********'),
                      AppSpacing.verticalXSpacing(space: 15.spMin),
                      RoundActionButton(
                        text: 'Change Password',
                        action: () => changePasswordBottomSheet,
                      ),
                    ],
                  ),
                ),
                AppCard(
                  bgColor: CustomColors.textWhite,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'App Settings',
                        style: AppTextStyle.styleSansSemiBold(16),
                      ),
                      Text(
                        'Notifications, appearance, etc.',
                        style: AppTextStyle.styleSansRegular(10),
                      ),
                      AppSpacing.verticalXSpacing(space: 15.spMin),
                      RoundActionButton(
                        text: 'Notifications',
                        action:
                            () => Navigator.pushNamed(
                              context,
                              Routes.notifications,
                            ),
                      ),
                      AppSpacing.verticalXSpacing(space: 15.spMin),
                      RoundActionButton(
                        text: 'Help and Support',
                        action:
                            () =>
                                Navigator.pushNamed(context, Routes.contactUs),
                      ),
                      AppSpacing.verticalXSpacing(space: 15.spMin),
                      RoundActionButton(
                        text: 'Log Out',
                        showGradiant: false,
                        bgColor: CustomColors.red,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget infoWidget({
    required String title,
    required String detail,
    Function()? action,
  }) => GestureDetector(
    onTap: action,
    child: Container(
      margin: EdgeInsets.symmetric(vertical: 10.spMin),
      padding: EdgeInsets.symmetric(vertical: 4.spMin, horizontal: 6.spMin),
      decoration: BoxDecoration(
        border: Border.all(color: CustomColors.textSecondaryLight),
        borderRadius: AppRoundBorder.circularXpx(),
      ),
      child: Column(
        spacing: 11.spMin,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: AppTextStyle.styleSansBold(
              8,
              fontColor: CustomColors.themeLight,
            ),
          ),
          Row(
            children: [
              Expanded(
                child: Text(detail, style: AppTextStyle.styleSansSemiBold(14)),
              ),
              SvgPicture.asset('assets/icons/edit.svg'),
            ],
          ),
        ],
      ),
    ),
  );

  void get changePasswordBottomSheet => showModalBottomSheet(
    showDragHandle: true,
    isScrollControlled: true,
    shape: RoundedRectangleBorder(borderRadius: AppRoundBorder.topRound30px),
    context: context,
    backgroundColor: CustomColors.canvas,
    builder:
        (context) => Container(
          width: double.infinity,
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ).add(AppSpacing.pagePaddingSmall),
          child: Column(
            spacing: 5.spMin,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Change Password',
                style: AppTextStyle.styleSansSemiBold(24),
              ),
              Text(
                'Set the new password for your account so you can login and access all the features.',
                style: AppTextStyle.styleSansRegular(
                  14,
                  fontColor: CustomColors.textLight,
                ),
              ),
              AppSpacing.verticalXSpacing(space: 15),
              AppTextField(hintText: 'Old Password', showHideTextIcon: true),
              AppTextField(hintText: 'New Password', showHideTextIcon: true),
              AppTextField(
                hintText: 'Re-Enter New Password',
                showHideTextIcon: true,
              ),
              RoundActionButton(
                text: 'Continue',
                action: () {
                  Navigator.pop(context);
                  successBottomSheet(
                    'Password Changed',
                    'You can login using your new password. A confirmation email has been sent to your email.',
                  );
                },
              ),
            ],
          ),
        ),
  );

  void get changeEmailBottomSheet => showModalBottomSheet(
    showDragHandle: true,
    isScrollControlled: true,
    shape: RoundedRectangleBorder(borderRadius: AppRoundBorder.topRound30px),
    context: context,
    backgroundColor: CustomColors.canvas,
    builder:
        (context) => Container(
          width: double.infinity,
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ).add(AppSpacing.pagePaddingSmall),
          child: Column(
            spacing: 5.spMin,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Change Email Address',
                style: AppTextStyle.styleSansSemiBold(24),
              ),
              Text(
                'Set the new Email for your account so you can login and access all the features.',
                style: AppTextStyle.styleSansRegular(
                  14,
                  fontColor: CustomColors.textLight,
                ),
              ),
              AppSpacing.verticalXSpacing(space: 15),
              AppTextField(hintText: 'Enter New Email Address'),
              AppSpacing.verticalXSpacing(space: 15),
              RoundActionButton(
                text: 'Continue',
                action: () {
                  Navigator.pop(context);
                  changeEmailOtpScreen;
                },
              ),
            ],
          ),
        ),
  );

  void get changeEmailOtpScreen {
    final pinController = TextEditingController();
    showModalBottomSheet(
      showDragHandle: true,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(borderRadius: AppRoundBorder.topRound30px),
      context: context,
      backgroundColor: CustomColors.canvas,
      builder:
          (context) => Container(
            width: double.infinity,
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ).add(AppSpacing.pagePaddingSmall),
            child: Column(
              spacing: 5.spMin,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Enter 4 Digits Code',
                  style: AppTextStyle.styleSansSemiBold(20),
                ),
                Text(
                  'Enter the 4 digits code that you received on your email.',
                  style: AppTextStyle.styleSansRegular(14),
                ),
                AppSpacing.verticalXSpacing(space: 15),
                Pinput(
                  preFilledWidget: Text(
                    '0',
                    style: AppTextStyle.styleSansSemiBold(
                      28,
                      fontColor: CustomColors.textLight,
                    ),
                  ),
                  controller: pinController,
                  length: 4,
                  defaultPinTheme: defaultPinTheme,
                  focusedPinTheme: focusedPinTheme,
                  submittedPinTheme: submittedPinTheme,
                  keyboardType: TextInputType.number,
                  pinputAutovalidateMode: PinputAutovalidateMode.onSubmit,
                  showCursor: true,
                  onCompleted: (pin) {},
                  separatorBuilder:
                      (index) => AppSpacing.horizontalXSpacing(space: 16),
                ),
                AppSpacing.verticalXSpacing(space: 15),
                RoundActionButton(
                  text: 'Continue',
                  action: () {
                    Navigator.pop(context);
                    successBottomSheet(
                      'Email Changed',
                      'You can login using your new email. A confirmation email has been sent to your email.',
                    );
                  },
                ),
              ],
            ),
          ),
    );
  }

  void successBottomSheet(String title, String detail) => showModalBottomSheet(
    showDragHandle: true,
    shape: RoundedRectangleBorder(borderRadius: AppRoundBorder.topRound30px),
    context: context,
    backgroundColor: CustomColors.canvas,
    builder:
        (context) => Container(
          width: double.infinity,
          padding: AppSpacing.pagePaddingSmall,
          child: Column(
            spacing: 35.spMin,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                spacing: 16.spMin,
                children: [
                  Image.asset('assets/icons/verify_success.png'),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      spacing: 5.spMin,
                      children: [
                        Text(title, style: AppTextStyle.styleSansSemiBold(20)),
                        Text(detail, style: AppTextStyle.styleSansRegular(14)),
                      ],
                    ),
                  ),
                ],
              ),
              RoundActionButton(
                text: 'Done',
                action: () => Navigator.pop(context),
              ),
            ],
          ),
        ),
  );
}
