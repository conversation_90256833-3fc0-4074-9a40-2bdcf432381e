import 'package:agnostic_healthcare/common_widgets/app_card.dart';
import 'package:agnostic_healthcare/common_widgets/round_action_button.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/utils/routes.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class AppointmentCard extends StatelessWidget {
  final bool upcoming;
  final bool isPast;
  final bool isCancelled;
  final bool canJoin;

  const AppointmentCard({
    super.key,
    this.upcoming = false,
    this.isPast = false,
    this.isCancelled = false,
    this.canJoin = false,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard(
      bgColor: CustomColors.textWhite,
      child: Column(
        spacing: 12.spMin,
        children: [
          Row(
            spacing: 14.spMin,
            children: [
              Container(
                decoration: BoxDecoration(
                  borderRadius: AppRoundBorder.circularXpx(pixel: 14),
                  color:
                      isPast
                          ? CustomColors.textSecondaryLight
                          : CustomColors.themeLight.withValues(alpha: .69),
                ),
                padding: EdgeInsets.symmetric(horizontal: 10.spMin),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '30',
                      style: AppTextStyle.styleSansSemiBold(
                        28,
                        fontColor: CustomColors.textWhite,
                      ),
                    ),
                    Text(
                      'AUG',
                      style: AppTextStyle.styleSansBold(
                        18,
                        fontColor: CustomColors.textWhite,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  spacing: 3.spMin,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      spacing: 5.spMin,
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 6.spMin),
                          decoration: BoxDecoration(
                            color:
                                isPast
                                    ? CustomColors.textSecondaryLight
                                    : CustomColors.themeLight,
                            borderRadius: AppRoundBorder.circularXpx(pixel: 12),
                          ),
                          child: Text(
                            'Appointment',
                            style: AppTextStyle.styleSansMedium(
                              8,
                              fontColor: CustomColors.textWhite,
                            ),
                          ),
                        ),
                        if (upcoming)
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 6.spMin),
                            decoration: BoxDecoration(
                              color: CustomColors.themeLight,
                              borderRadius: AppRoundBorder.circularXpx(
                                pixel: 12,
                              ),
                            ),
                            child: Text(
                              'Upcoming',
                              style: AppTextStyle.styleSansMedium(
                                8,
                                fontColor: CustomColors.textWhite,
                              ),
                            ),
                          ),
                        if (isPast)
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 6.spMin),
                            decoration: BoxDecoration(
                              color: CustomColors.textSecondaryLight,
                              borderRadius: AppRoundBorder.circularXpx(
                                pixel: 12,
                              ),
                            ),
                            child: Text(
                              isCancelled ? 'Cancelled' : 'Attended',
                              style: AppTextStyle.styleSansMedium(
                                8,
                                fontColor: CustomColors.textWhite,
                              ),
                            ),
                          ),
                      ],
                    ),
                    Text(
                      'Dr. Aaron Smith | ABC Clinic',
                      style: AppTextStyle.styleSansSemiBold(16),
                    ),
                    Row(
                      spacing: 4.spMin,
                      children: [
                        Text(
                          'Cardiologist',
                          style: AppTextStyle.styleSansRegular(
                            10,
                            fontColor: CustomColors.textLight,
                          ),
                        ),
                        SizedBox(),
                        SvgPicture.asset(
                          'assets/icons/video.svg',
                          color: CustomColors.textLight,
                        ),
                        Text(
                          'Online',
                          style: AppTextStyle.styleSansRegular(
                            10,
                            fontColor: CustomColors.textLight,
                          ),
                        ),
                        SizedBox(),
                        SvgPicture.asset(
                          'assets/icons/time.svg',
                          color: CustomColors.textLight,
                        ),
                        Text(
                          '10:30am - 5:30pm',
                          style: AppTextStyle.styleSansRegular(
                            10,
                            fontColor: CustomColors.textLight,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (canJoin)
            RoundActionButton(
              text: 'Join Consult',
              action: () => Navigator.pushNamed(context, Routes.call),
            ),
        ],
      ),
    );
  }
}
