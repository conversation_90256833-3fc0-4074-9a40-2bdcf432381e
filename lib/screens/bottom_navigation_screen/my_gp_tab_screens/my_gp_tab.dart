import 'package:agnostic_healthcare/common_widgets/custom_app_bar.dart';
import 'package:agnostic_healthcare/common_widgets/profile_image_widget.dart';
import 'package:agnostic_healthcare/common_widgets/round_action_button.dart';
import 'package:agnostic_healthcare/constants/data_constants.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/screens/bottom_navigation_screen/my_gp_tab_screens/sub_widgets/appointment_card.dart';
import 'package:agnostic_healthcare/utils/routes.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class MyGpTab extends StatefulWidget {
  const MyGpTab({super.key});

  @override
  State<MyGpTab> createState() => _MyGpTabState();
}

class _MyGpTabState extends State<MyGpTab> {
  bool showUpcomingApp = false;
  bool showPastApp = false;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: Image.asset('assets/images/splash_bg.png', fit: BoxFit.fill),
        ),
        Scaffold(
          backgroundColor: Colors.transparent,
          appBar: CustomAppBar(
            title: '',
            centerTitle: false,
            titleWidget: Expanded(
              child: Row(
                spacing: 4.spMin,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  SvgPicture.asset('assets/icons/gp.svg', height: 50.spMin),
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'My GP',
                          style: AppTextStyle.styleSansSemiBold(20),
                        ),
                        Text(
                          'Your GP Appointments',
                          style: AppTextStyle.styleSansRegular(
                            12,
                            fontColor: CustomColors.textLight,
                          ),
                        ),
                      ],
                    ),
                  ),
                  ProfileImageWidget(imageSize: 56, imageUrl: profile),
                ],
              ),
            ),
          ),
          body: SingleChildScrollView(
            padding: AppSpacing.pagePaddingSmall,
            child: Column(
              spacing: 15.spMin,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Upcoming Appointment',
                      style: AppTextStyle.styleSansSemiBold(16),
                    ),
                    Text(
                      'See all',
                      style: AppTextStyle.styleSansSemiBold(
                        12,
                        fontColor: CustomColors.themeLight,
                      ),
                    ),
                  ],
                ),
                if (showUpcomingApp)
                  ...List.generate(2, (i) => AppointmentCard(upcoming: i == 0))
                else
                  InkWell(
                    onTap: () {
                      showUpcomingApp = true;
                      setState(() {});
                    },
                    child: Padding(
                      padding: AppSpacing.pageVerticalPadding,
                      child: Column(
                        children: [
                          SvgPicture.asset('assets/icons/calendar.svg'),
                          Text(
                            'No Upcoming Appointments',
                            style: AppTextStyle.styleSansMedium(
                              15,
                              fontColor: CustomColors.textLight,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                RoundActionButton(
                  startIcon: SvgPicture.asset('assets/icons/new_app.svg'),
                  text: 'Book Appointment',
                  action: () => showBookingBottomSheet,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Past Appointment',
                      style: AppTextStyle.styleSansSemiBold(16),
                    ),
                    Text(
                      'See all',
                      style: AppTextStyle.styleSansSemiBold(
                        12,
                        fontColor: CustomColors.themeLight,
                      ),
                    ),
                  ],
                ),
                if (showPastApp)
                  ...List.generate(2, (i) => AppointmentCard(isPast: true))
                else
                  InkWell(
                    onTap: () {
                      showPastApp = true;
                      setState(() {});
                    },
                    child: Padding(
                      padding: AppSpacing.pageVerticalPadding,
                      child: Column(
                        children: [
                          SvgPicture.asset('assets/icons/calendar.svg'),
                          Text(
                            'No Past Appointments',
                            style: AppTextStyle.styleSansMedium(
                              15,
                              fontColor: CustomColors.textLight,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void get showBookingBottomSheet =>
      showModalBottomSheet(
        isDismissible: false,
        showDragHandle: true,
        shape: RoundedRectangleBorder(
            borderRadius: AppRoundBorder.topRound30px),
        context: context,
        backgroundColor: CustomColors.textWhite,
        isScrollControlled: true,
        builder:
            (context) =>
            Container(
              width: double.infinity,
              padding: AppSpacing.leftRightSpacingLarge,
              child: Column(
                spacing: 15.spMin,
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Image.asset('assets/icons/verify_failed.png'),
                  Text(
                    'Are you showing these Symptoms?',
                    style: AppTextStyle.styleSansSemiBold(
                      18,
                      fontColor: CustomColors.red,
                    ),
                  ),
                  Text(
                    'If you have any of these symptoms\n'
                        '• Shortness of Breath\n'
                        '• Chest Pain or Pressure\n'
                        '• Severe Headache\n'
                        '• Loss of Consciousness or Fainting\n'
                        '• Uncontrollable Bleeding\n'
                        '• Sudden Weakness or Numbness\n'
                        '• High Fever with Other Symptoms\n'
                        '• Severe Abdominal Pain\n'
                        '• Signs of a Severe Allergic Reaction\n  (Anaphylaxis)\n'
                        '• Suicidal Thoughts or Self-Harm\n'
                        'Call 111 Immediately.',
                    textAlign: TextAlign.left,
                    style: AppTextStyle.styleSansRegular(
                      16,
                      fontColor: CustomColors.textLight,
                    ),
                  ),
                  AppSpacing.verticalXSpacing(space: 6),
                  RoundActionButton(
                    bgColor: CustomColors.red,
                    action: () {
                      Navigator.pop(context);
                    },
                    showGradiant: false,
                    text: 'Call 911',
                  ),
                  RoundActionButton(
                    action:
                        () =>
                        Navigator.pushReplacementNamed(
                          context,
                          Routes.bookAppointment,
                        ),
                    text: 'Continue',
                  ),
                ],
              ),
            ),
      );
}
