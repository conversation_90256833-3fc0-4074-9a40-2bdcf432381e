import 'package:agnostic_healthcare/common_widgets/app_card.dart';
import 'package:agnostic_healthcare/common_widgets/icon_button.dart';
import 'package:agnostic_healthcare/common_widgets/profile_image_widget.dart';
import 'package:agnostic_healthcare/common_widgets/round_action_button.dart';
import 'package:agnostic_healthcare/constants/data_constants.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/providers/providers.dart';
import 'package:agnostic_healthcare/utils/routes.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';


class HomeTab extends StatefulWidget {
  const HomeTab({super.key});

  @override
  State<HomeTab> createState() => _HomeTabState();
}

class _HomeTabState extends State<HomeTab> {
  late UserProvider userProvider;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    userProvider = Provider.of<UserProvider>(context);
  }

  @override
  void initState() {
    super.initState();
    // Initialize with mock data for demonstration
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // CHANGE THIS TO TEST DIFFERENT USER TYPES:
      // true = verified user (shows appointments)
      // false = unverified user (shows unlock medical records)
      userProvider.loginWithMockUser(isVerified: true);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(130.spMin),
        child: Stack(
          children: [
            Positioned.fill(
              child: SvgPicture.asset(
                'assets/icons/home_top.svg',
                fit: BoxFit.fill,
              ),
            ),
            SafeArea(
              child: Padding(
                padding: AppSpacing.pagePaddingSmall,
                child: Row(
                  spacing: 8.spMin,
                  children: [
                    ProfileImageWidget(imageSize: 56, imageUrl: profile),
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      spacing: 4.spMin,
                      children: [
                        Text(
                          'Welcome',
                          style: AppTextStyle.styleSansRegular(
                            14,
                            fontColor: CustomColors.textWhite,
                          ),
                        ),
                        Text(
                          userProvider.currentUser?.name ?? 'User',
                          style: AppTextStyle.styleSansMedium(
                            20,
                            fontColor: CustomColors.textWhite,
                          ),
                        ),
                      ],
                    ),
                    Spacer(),
                    AppIconButton(
                      action:
                          () => Navigator.pushNamed(
                            context,
                            Routes.notifications,
                          ),
                      bgColor: Colors.transparent,
                      padding: 0,
                      icon: SvgPicture.asset(
                        'assets/icons/notification_active.svg',
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      body: SingleChildScrollView(
        padding: AppSpacing.pagePaddingExtraSmall,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 15.spMin,
          children: [
            if (userProvider.isFullyVerified) ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Upcoming Appointment',
                    style: AppTextStyle.styleSansSemiBold(16),
                  ),
                  GestureDetector(
                    onTap: () => Navigator.pushNamed(context, Routes.myAppointments),
                    child: Text(
                      'See all',
                      style: AppTextStyle.styleSansSemiBold(
                        12,
                        fontColor: CustomColors.themeLight,
                      ),
                    ),
                  ),
                ],
              ),
              Consumer<UserProvider>(
                builder: (context, provider, child) {
                  final nextAppointment = provider.getNextAppointment();

                  if (nextAppointment == null) {
                    return AppCard(
                      action: () => Navigator.pushNamed(context, Routes.bookAppointment),
                      bgColor: CustomColors.lightGreyBackground.withValues(
                        alpha: .12,
                      ),
                      child: SizedBox(
                        height: 80.spMin,
                        child: Center(
                          child: Row(
                            spacing: 7.spMin,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.error_outline_outlined,
                                color: CustomColors.textLight,
                              ),
                              Text(
                                'No Upcoming Appointment',
                                style: AppTextStyle.styleSansMedium(
                                  16,
                                  fontColor: CustomColors.textLight,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  }

                  return AppCard(
                    gradient: nextAppointment.canJoin
                        ? AppGradients.upcomingAppGradient
                        : null,
                    action: () {
                      if (nextAppointment.canJoin) {
                        Navigator.pushNamed(context, Routes.call);
                      }
                    },
                    child: Column(
                      spacing: 12.spMin,
                      children: [
                        Row(
                          spacing: 14.spMin,
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                borderRadius: AppRoundBorder.circularXpx(
                                  pixel: 14,
                                ),
                                color: CustomColors.textGrey,
                              ),
                              padding: EdgeInsets.symmetric(
                                horizontal: 9.spMin,
                                vertical: 4.spMin,
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    nextAppointment.appointmentDate.day.toString(),
                                    style: AppTextStyle.styleSansSemiBold(
                                      28,
                                      fontColor: CustomColors.themeLight,
                                    ),
                                  ),
                                  Text(
                                    _getMonthAbbreviation(nextAppointment.appointmentDate.month),
                                    style: AppTextStyle.styleSansBold(
                                      18,
                                      fontColor: CustomColors.textWhite,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Expanded(
                              child: Column(
                                spacing: 3.spMin,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    spacing: 5.spMin,
                                    children: [
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: 6.spMin,
                                        ),
                                        decoration: BoxDecoration(
                                          color: nextAppointment.canJoin
                                              ? CustomColors.themeLight
                                              : CustomColors.text,
                                          borderRadius: AppRoundBorder.circularXpx(
                                            pixel: 12,
                                          ),
                                        ),
                                        child: Text(
                                          'Appointment',
                                          style: AppTextStyle.styleSansMedium(
                                            8,
                                            fontColor: CustomColors.textWhite,
                                          ),
                                        ),
                                      ),
                                      if (nextAppointment.canJoin)
                                        Container(
                                          padding: EdgeInsets.symmetric(
                                            horizontal: 6.spMin,
                                          ),
                                          decoration: BoxDecoration(
                                            color: CustomColors.themeLight,
                                            borderRadius: AppRoundBorder.circularXpx(
                                              pixel: 12,
                                            ),
                                          ),
                                          child: Text(
                                            'Join Consult',
                                            style: AppTextStyle.styleSansMedium(
                                              8,
                                              fontColor: CustomColors.textWhite,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                  Text(
                                    nextAppointment.canJoin
                                        ? '${nextAppointment.doctorName} | ${nextAppointment.clinicName}'
                                        : nextAppointment.doctorName,
                                    style: AppTextStyle.styleSansSemiBold(
                                      16,
                                      fontColor: CustomColors.textWhite,
                                    ),
                                  ),
                                  Row(
                                    spacing: 4.spMin,
                                    children: [
                                      Text(
                                        nextAppointment.specialty,
                                        style: AppTextStyle.styleSansRegular(
                                          12,
                                          fontColor: CustomColors.textWhite,
                                        ),
                                      ),
                                      SizedBox(),
                                      SvgPicture.asset('assets/icons/time.svg'),
                                      Text(
                                        nextAppointment.timeSlot,
                                        style: AppTextStyle.styleSansRegular(
                                          12,
                                          fontColor: CustomColors.textWhite,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        if (nextAppointment.canJoin)
                          RoundActionButton(
                            text: 'Join Consult',
                            bgColor: CustomColors.textGrey,
                            showGradiant: false,
                            action: () => Navigator.pushNamed(context, Routes.call),
                          ),
                      ],
                    ),
                  );
                },
              ),
            ] else
              AppCard(
                child: Column(
                  spacing: 10.spMin,
                  children: [
                    Row(
                      spacing: 8.spMin,
                      children: [
                        Image.asset('assets/icons/unlock_records.png'),
                        Text(
                          'Unlock Your Medical Records',
                          style: AppTextStyle.styleSansBold(
                            18,
                            fontColor: CustomColors.textWhite,
                          ),
                        ),
                      ],
                    ),
                    Text(
                      'Verify your identity and Add your Medical Records to book with your GP and get started',
                      style: AppTextStyle.styleSansRegular(
                        14,
                        fontColor: CustomColors.textWhite,
                      ),
                    ),
                    RoundActionButton(
                      action:
                          () => Navigator.pushNamed(
                            context,
                            Routes.verifyIdentity,
                          ),
                      text: 'Verify Your Identity',
                      fontSize: 14,
                    ),
                  ],
                ),
              ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Record',
                  style: AppTextStyle.styleSansSemiBold(16),
                ),
                Consumer<UserProvider>(
                  builder: (context, provider, child) {
                    final recentRecord = provider.getRecentMedicineRecord();
                    if (recentRecord != null) {
                      return GestureDetector(
                        onTap: () => Navigator.pushNamed(context, Routes.medications),
                        child: Text(
                          'See all',
                          style: AppTextStyle.styleSansSemiBold(
                            12,
                            fontColor: CustomColors.themeLight,
                          ),
                        ),
                      );
                    }
                    return SizedBox.shrink();
                  },
                ),
              ],
            ),
            Consumer<UserProvider>(
              builder: (context, provider, child) {
                final recentRecord = provider.getRecentMedicineRecord();

                if (recentRecord != null) {
                  return AppCard(
                    bgColor: CustomColors.textWhite,
                    action: () => Navigator.pushNamed(context, Routes.medications),
                    child: Row(
                      spacing: 14.spMin,
                      children: [
                        Image.asset('assets/icons/medicine.png'),
                        Expanded(
                          child: Column(
                            spacing: 3.spMin,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 6.spMin),
                                decoration: BoxDecoration(
                                  color: CustomColors.themeLight,
                                  borderRadius: AppRoundBorder.circularXpx(pixel: 12),
                                ),
                                child: Text(
                                  'Medication',
                                  style: AppTextStyle.styleSansMedium(
                                    8,
                                    fontColor: CustomColors.textWhite,
                                  ),
                                ),
                              ),
                              Text(
                                '${recentRecord.dosage} ${recentRecord.medicationName}',
                                style: AppTextStyle.styleSansSemiBold(16),
                              ),
                              Text.rich(
                                TextSpan(
                                  children: [
                                    TextSpan(
                                      text: 'Prescribed by ${recentRecord.prescribedBy} | ',
                                      style: AppTextStyle.styleSansRegular(12),
                                    ),
                                    TextSpan(
                                      text: _formatDate(recentRecord.prescribedDate),
                                      style: AppTextStyle.styleSansMedium(
                                        12,
                                        fontColor: CustomColors.themeLight,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return AppCard(
                  action: () => Navigator.pushNamed(context, Routes.medications),
                  bgColor: CustomColors.lightGreyBackground.withValues(
                    alpha: .12,
                  ),
                  child: SizedBox(
                    height: 80.spMin,
                    child: Center(
                      child: Row(
                        spacing: 7.spMin,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.error_outline_outlined,
                            color: CustomColors.textLight,
                          ),
                          Text(
                            'No Recent Record',
                            style: AppTextStyle.styleSansMedium(
                              16,
                              fontColor: CustomColors.textLight,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
            Text(
              'Wellbeing For You',
              style: AppTextStyle.styleSansSemiBold(16),
            ),
            AppCard(
              action:
                  () => Navigator.pushNamed(
                    context,
                    Routes.wellbeingDetail,
                    arguments: [
                      'Mental Health',
                      '🧠 Mindful Wellbeing',
                      'Your mental health is just as important as your physical health. important as your physical health.',
                    ],
                  ),
              bgColor: CustomColors.textWhite,
              child: Column(
                spacing: 3.spMin,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 6.spMin),
                    decoration: BoxDecoration(
                      color: CustomColors.themeLight,
                      borderRadius: AppRoundBorder.circularXpx(pixel: 12),
                    ),
                    child: Text(
                      'Mental Health',
                      style: AppTextStyle.styleSansMedium(
                        8,
                        fontColor: CustomColors.textWhite,
                      ),
                    ),
                  ),
                  Text(
                    '🧠 Mindful Wellbeing',
                    style: AppTextStyle.styleSansSemiBold(16),
                  ),
                  Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                          text:
                              'Your mental health is just as important as your physical health. important as your physical health. ',
                          style: AppTextStyle.styleSansRegular(12),
                        ),
                        TextSpan(
                          text: 'Read More',
                          style: AppTextStyle.styleSansMedium(
                            12,
                            fontColor: CustomColors.themeLight,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            AppCard(
              action:
                  () => Navigator.pushNamed(
                    context,
                    Routes.wellbeingDetail,
                    arguments: [
                      'Nutrition',
                      '🍽️ Nutrition & Diet',
                      'Fuel your body with the right foods. Focus on a balanced diet. important as your physical health.',
                    ],
                  ),
              bgColor: CustomColors.textWhite,
              child: Column(
                spacing: 3.spMin,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 6.spMin),
                    decoration: BoxDecoration(
                      color: CustomColors.themeLight,
                      borderRadius: AppRoundBorder.circularXpx(pixel: 12),
                    ),
                    child: Text(
                      'Nutrition',
                      style: AppTextStyle.styleSansMedium(
                        8,
                        fontColor: CustomColors.textWhite,
                      ),
                    ),
                  ),
                  Text(
                    '🍽️ Nutrition & Diet',
                    style: AppTextStyle.styleSansSemiBold(16),
                  ),
                  Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                          text:
                              'Fuel your body with the right foods. Focus on a balanced diet. important as your physical health. ',
                          style: AppTextStyle.styleSansRegular(12),
                        ),
                        TextSpan(
                          text: 'Read More',
                          style: AppTextStyle.styleSansMedium(
                            12,
                            fontColor: CustomColors.themeLight,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            AppCard(
              action:
                  () => Navigator.pushNamed(
                    context,
                    Routes.wellbeingDetail,
                    arguments: [
                      'Activity',
                      '🏃 Activity and Fitness',
                      'Move your body every day! Whether it\'s a brisk walk, a bike ride.  important as your physical health.',
                    ],
                  ),
              bgColor: CustomColors.textWhite,
              child: Column(
                spacing: 3.spMin,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 6.spMin),
                    decoration: BoxDecoration(
                      color: CustomColors.themeLight,
                      borderRadius: AppRoundBorder.circularXpx(pixel: 12),
                    ),
                    child: Text(
                      'Activity',
                      style: AppTextStyle.styleSansMedium(
                        8,
                        fontColor: CustomColors.textWhite,
                      ),
                    ),
                  ),
                  Text(
                    '🏃 Activity and Fitness',
                    style: AppTextStyle.styleSansSemiBold(16),
                  ),
                  Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                          text:
                              'Move your body every day! Whether it\'s a brisk walk, a bike ride.  important as your physical health. ',
                          style: AppTextStyle.styleSansRegular(12),
                        ),
                        TextSpan(
                          text: 'Read More',
                          style: AppTextStyle.styleSansMedium(
                            12,
                            fontColor: CustomColors.themeLight,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to get month abbreviation
  String _getMonthAbbreviation(int month) {
    const months = [
      'JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN',
      'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'
    ];
    return months[month - 1];
  }

  // Helper method to format date
  String _formatDate(DateTime date) {
    return '${date.day} ${_getMonthAbbreviation(date.month)} ${date.year}';
  }
}
