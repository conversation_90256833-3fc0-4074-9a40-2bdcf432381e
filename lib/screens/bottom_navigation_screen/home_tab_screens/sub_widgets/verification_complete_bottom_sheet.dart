import 'package:agnostic_healthcare/common_widgets/round_action_button.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/providers/providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

void verificationCompleteBottomSheet(
  BuildContext context,
  bool isSuccess,
) => showModalBottomSheet(
  isDismissible: false,
  showDragHandle: true,
  shape: RoundedRectangleBorder(borderRadius: AppRoundBorder.topRound30px),
  context: context,
  backgroundColor: CustomColors.textWhite,
  builder:
      (context) => Container(
        width: double.infinity,
        padding: AppSpacing.leftRightSpacingLarge,
        child: Column(
          spacing: 5.spMin,
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              'assets/icons/${isSuccess ? 'verify_success' : 'verify_failed'}.png',
            ),
            Text(
              isSuccess
                  ? 'Verification Complete!'
                  : 'We need a little help to Finish!',
              textAlign: TextAlign.center,
              style: AppTextStyle.styleSansSemiBold(16),
            ),
            Text(
              isSuccess
                  ? 'You can now access your full health record and book appointments with your GP'
                  : 'We couldn\'t automatically link your record. For your security, our support team will manually review your application. We\'ll notify you within 24 hours once it\'s complete.',
              textAlign: TextAlign.center,
              style: AppTextStyle.styleSansSemiBold(16),
            ),
            AppSpacing.verticalXSpacing(space: 6),
            RoundActionButton(
              text: 'Go to Home',
              showGradiant: isSuccess,
              bgColor: isSuccess ? null : CustomColors.textLight,
              action: () async {
                Provider.of<UserProvider>(
                  context,
                  listen: false,
                ).setVerification(isSuccess);
                Navigator.popUntil(context, (route) => route.isFirst);
              },
            ),
          ],
        ),
      ),
);
