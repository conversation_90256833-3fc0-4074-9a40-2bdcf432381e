import 'dart:io';
import 'dart:math';

import 'package:agnostic_healthcare/common_widgets/app_text_field.dart';
import 'package:agnostic_healthcare/common_widgets/custom_app_bar.dart';
import 'package:agnostic_healthcare/common_widgets/custom_dropdown.dart';
import 'package:agnostic_healthcare/common_widgets/loading_dialog.dart';
import 'package:agnostic_healthcare/common_widgets/profile_image_widget.dart';
import 'package:agnostic_healthcare/common_widgets/round_action_button.dart';
import 'package:agnostic_healthcare/constants/data_constants.dart';
import 'package:agnostic_healthcare/constants/enums.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/screens/bottom_navigation_screen/home_tab_screens/sub_widgets/verification_complete_bottom_sheet.dart';
import 'package:agnostic_healthcare/utils/routes.dart';
import 'package:expandable_page_view/expandable_page_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class VerifyGpDetailsScreen extends StatefulWidget {
  const VerifyGpDetailsScreen({super.key});

  @override
  State<VerifyGpDetailsScreen> createState() => _VerifyGpDetailsScreenState();
}

class _VerifyGpDetailsScreenState extends State<VerifyGpDetailsScreen> {
  PageController pageController = PageController();

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: Image.asset('assets/images/splash_bg.png', fit: BoxFit.fill),
        ),
        Scaffold(
          backgroundColor: Colors.transparent,
          appBar: CustomAppBar(
            title: '',
            centerTitle: false,
            titleWidget: Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Verify with GP Details',
                          style: AppTextStyle.styleSansSemiBold(20),
                        ),
                        Text(
                          'Enter your personal Details',
                          style: AppTextStyle.styleSansRegular(
                            12,
                            fontColor: CustomColors.textLight,
                          ),
                        ),
                      ],
                    ),
                  ),
                  ProfileImageWidget(imageSize: 56, imageUrl: profile),
                ],
              ),
            ),
          ),
          body: ExpandablePageView(
            controller: pageController,
            physics: NeverScrollableScrollPhysics(),
            children: [personalDetailsPage, residentialDetailsPage],
          ),
        ),
      ],
    );
  }

  Gender gender = Gender.male;
  String? practice;

  Widget get personalDetailsPage => SingleChildScrollView(
    padding: AppSpacing.pagePaddingSmall,
    child: Column(
      spacing: 12.spMin,
      children: [
        Row(
          spacing: 16.spMin,
          children: [
            Image.asset('assets/icons/gp_details.png'),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Enter Your Details',
                    style: AppTextStyle.styleSansBold(16),
                  ),
                  Text(
                    'Please enter your details exactly as they appear on your official ID.',
                    style: AppTextStyle.styleSansRegular(
                      12,
                      fontColor: CustomColors.textLight,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        SizedBox(),
        AppTextField(title: 'First Name', hintText: 'Enter first name'),
        AppTextField(title: 'Last Name', hintText: 'Enter last name'),
        AppTextField(
          title: 'Date of Birth',
          hintText: 'DD/MM/YYYY',
          isReadOnly: true,
          onTap: () async {
            DateTime? pickedDate = await showDatePicker(
              context: context,
              firstDate: DateTime(1940),
              lastDate: DateTime.now(),
            );
            if (pickedDate != null) {}
          },
        ),
        CustomDropdown<Gender>(
          title: 'Gender',
          options: Gender.values,
          onSelect: (selection) => gender = selection,
          selection: gender,
        ),
        SizedBox(),
        RoundActionButton(text: 'Next', action: () => showScanBottomSheet),
      ],
    ),
  );

  void get showScanBottomSheet => showModalBottomSheet(
    isDismissible: false,
    showDragHandle: true,
    shape: RoundedRectangleBorder(borderRadius: AppRoundBorder.topRound30px),
    context: context,
    backgroundColor: CustomColors.textWhite,
    builder:
        (context) => Container(
          width: double.infinity,
          padding: AppSpacing.leftRightSpacingLarge,
          child: Column(
            spacing: 5.spMin,
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset('assets/icons/scan_id.png'),
              Text(
                'you\'ll need to scan your ID. Please have your NZ Driver Licence, Birth Certificate, Citizenship certificate, 18+ Card or Passport ready.',
                textAlign: TextAlign.center,
                style: AppTextStyle.styleSansSemiBold(16),
              ),
              AppSpacing.verticalXSpacing(space: 6),
              RoundActionButton(
                text: 'Scan Document',
                action: () async {
                  File? imageFile =
                      await Navigator.pushReplacementNamed(
                            context,
                            Routes.scanDoc,
                          )
                          as File?;
                  if (imageFile != null) {
                    pageController.jumpToPage(1);
                  }
                },
              ),
            ],
          ),
        ),
  );

  Widget get residentialDetailsPage => SingleChildScrollView(
    padding: AppSpacing.pagePaddingSmall,
    child: Column(
      spacing: 12.spMin,
      children: [
        Row(
          spacing: 16.spMin,
          children: [
            Image.asset('assets/icons/gp_details.png'),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Enter Your address and practice',
                    style: AppTextStyle.styleSansBold(16),
                  ),
                  Text(
                    'Please enter your details exactly as they appear on your official ID.',
                    style: AppTextStyle.styleSansRegular(
                      12,
                      fontColor: CustomColors.textLight,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        SizedBox(),
        AppTextField(
          title: 'Residential Address',
          hintText: 'House No, Street No',
        ),
        AppTextField(hintText: 'Area Name, Avenue Name'),
        AppTextField(hintText: 'State, City'),

        CustomDropdown(
          title: 'GP Practice',
          options: [],
          onSelect: (selection) {},
          hint: 'Select Practice',
          selection: practice,
        ),
        SizedBox(),
        RoundActionButton(
          text: 'Verify',
          action: () {
            showDialog(
              context: context,
              barrierDismissible: false,
              // Prevents closing the dialog by tapping outside
              builder: (BuildContext context) {
                return const LoadingDialog(message: 'Verifying your details');
              },
            );
            Future.delayed(Duration(seconds: 2), () {
              verificationCompleteBottomSheet(context, Random().nextBool());
            });
          },
        ),
      ],
    ),
  );
}
