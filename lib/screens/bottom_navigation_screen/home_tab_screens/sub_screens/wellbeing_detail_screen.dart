import 'package:agnostic_healthcare/common_widgets/icon_button.dart';
import 'package:agnostic_healthcare/common_widgets/network_image.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class WellbeingDetailScreen extends StatelessWidget {
  final String category;
  final String title;
  final String subtitle;

  const WellbeingDetailScreen({
    super.key,
    required this.category,
    required this.title,
    required this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    final Map<String, dynamic> data = {
      'Mental Health': {
        'image':
            'https://images.unsplash.com/photo-1604480132736-44c188fe4d20?q=80&w=1590&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        'content':
            'Mental health encompasses our emotional, psychological, and social well-being. It affects how we think, feel, and act. It also helps determine how we handle stress, relate to others, and make choices. Mental health is important at every stage of life, from childhood and adolescence through adulthood. Taking care of your mind through meditation, therapy, or simple daily mindfulness can have a profound impact on your overall quality of life.',
      },
      'Nutrition': {
        'image':
            'https://plus.unsplash.com/premium_photo-1664476002571-ead0cbfc6d74?q=80&w=1470&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        'content':
            'Nutrition is the study of nutrients in food, how the body uses them, and the relationship between diet, health, and disease. It is the process by which living things take in food and use it for growth, metabolism, and repair. A balanced diet provides your body with the essential nutrients, vitamins, and minerals it needs to function properly. Remember to stay hydrated and listen to your body.',
      },
      'Activity': {
        'image':
            'https://plus.unsplash.com/premium_photo-1683442814148-78aa260ac18e?q=80&w=1470&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        'content':
            'Physical activity is a powerful tool for improving both physical and mental health. Regular exercise helps to manage weight, lower blood pressure, improve sleep, and reduce the risk of many chronic diseases. It also boosts your mood and energy levels. Find an activity you enjoy, whether it\'s running, dancing, or yoga, and make it a regular part of your routine. Consistency is key!',
      },
    };

    final selectedData =
        data[category] ??
        data['Mental Health']; // Default to Mental Health if data is not found

    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Stack(
              children: [
                AppNetworkImage(
                  url: selectedData['image'],
                  imageHeight: 300,
                  imageWidth: double.infinity,
                ),
                Positioned(
                  top: 20,
                  left: 20,
                  child: SafeArea(
                    child: AppIconButton(
                      iconAlignment: Alignment.centerLeft,
                      bgColor: Colors.transparent,
                      action: () => Navigator.pop(context),
                      icon: SvgPicture.asset('assets/icons/back.svg'),
                      padding: 0.spMin,
                      size: 30,
                    ),
                  ),
                ),
              ],
            ),

            // Content section
            Padding(
              padding: AppSpacing.pagePaddingSmall,
              child: Column(
                spacing: 10.spMin,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(title, style: AppTextStyle.styleSansBold(24)),
                  Text(
                    selectedData['content'],
                    style: AppTextStyle.styleSansRegular(14),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
