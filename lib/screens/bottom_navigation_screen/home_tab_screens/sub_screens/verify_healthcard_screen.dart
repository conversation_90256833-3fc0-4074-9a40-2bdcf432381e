import 'dart:io';
import 'dart:math';

import 'package:agnostic_healthcare/common_widgets/custom_app_bar.dart';
import 'package:agnostic_healthcare/common_widgets/icon_button.dart';
import 'package:agnostic_healthcare/common_widgets/profile_image_widget.dart';
import 'package:agnostic_healthcare/common_widgets/round_action_button.dart';
import 'package:agnostic_healthcare/constants/data_constants.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/screens/bottom_navigation_screen/home_tab_screens/sub_widgets/verification_complete_bottom_sheet.dart';
import 'package:agnostic_healthcare/utils/general_methods.dart';
import 'package:agnostic_healthcare/utils/routes.dart';
import 'package:flutter/material.dart';
import 'package:flutter_decorated_container/flutter_decorated_container.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class VerifyHealthCardScreen extends StatefulWidget {
  const VerifyHealthCardScreen({super.key});

  @override
  State<VerifyHealthCardScreen> createState() => _VerifyHealthCardScreenState();
}

class _VerifyHealthCardScreenState extends State<VerifyHealthCardScreen> {
  File? selectedFile;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: Image.asset('assets/images/splash_bg.png', fit: BoxFit.fill),
        ),
        Scaffold(
          appBar: CustomAppBar(
            title: '',
            centerTitle: false,
            titleWidget: Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Verify with Documents',
                          style: AppTextStyle.styleSansSemiBold(20),
                        ),
                        Text(
                          'Upload your Health Documents',
                          style: AppTextStyle.styleSansRegular(
                            12,
                            fontColor: CustomColors.textLight,
                          ),
                        ),
                      ],
                    ),
                  ),
                  ProfileImageWidget(imageSize: 56, imageUrl: profile),
                ],
              ),
            ),
          ),
          backgroundColor: Colors.transparent,
          body: SingleChildScrollView(
            padding: AppSpacing.pagePaddingLarge,
            child: Column(
              spacing: 12.spMin,
              children: [
                Row(
                  spacing: 16.spMin,
                  children: [
                    Image.asset('assets/icons/upload_health_card.png'),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Upload Your Health Document',
                            style: AppTextStyle.styleSansBold(16),
                          ),
                          Text(
                            'Please provide a clear photo of a recent prescription or lab result. Make sure your full name and NHI number are visible.',
                            style: AppTextStyle.styleSansRegular(
                              12,
                              fontColor: CustomColors.textLight,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                AppSpacing.verticalXSpacing(space: 34),
                DecoratedContainer(
                  cornerRadius: 14,
                  backgroundColor: CustomColors.canvas,
                  strokeColor: CustomColors.textLight,
                  padding: EdgeInsets.all(selectedFile == null ? 16.spMin : 0),
                  child:
                      selectedFile != null
                          ? ClipRRect(
                            borderRadius: AppRoundBorder.circularXpx(pixel: 14),
                            child: Stack(
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    color: CustomColors.textWhite,
                                  ),
                                  child: Row(
                                    spacing: 17.spMin,
                                    children: [
                                      SizedBox(
                                        height: 80.spMin,
                                        width: 85.spMin,
                                        child: Image.file(
                                          selectedFile!,
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                      Expanded(
                                        child: Text(
                                          GeneralMethods.getFileNameFromPath(
                                            selectedFile!.path,
                                          ),
                                          style: AppTextStyle.styleSansSemiBold(
                                            16,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Positioned(
                                  right: 5.spMin,
                                  top: 5.spMin,
                                  child: AppIconButton(
                                    action: () {
                                      selectedFile = null;
                                      setState(() {});
                                    },
                                    size: 20,
                                    padding: 0,
                                    icon: Icon(
                                      Icons.close,
                                      color: CustomColors.text,
                                      size: 15,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          )
                          : Center(
                            child: Column(
                              children: [
                                SvgPicture.asset('assets/icons/upload.svg'),
                                Text(
                                  'Upload Your Document',
                                  style: AppTextStyle.styleSansRegular(
                                    16,
                                    fontColor: CustomColors.textLight,
                                  ),
                                ),
                              ],
                            ),
                          ),
                ),
                SizedBox(),
                RoundActionButton(
                  startIcon: SvgPicture.asset('assets/icons/upload_arrow.svg'),
                  text: 'Upload',
                  action:
                      () => verificationCompleteBottomSheet(
                        context,
                        Random().nextBool(),
                      ),
                ),
                RoundActionButton(
                  showGradiant: false,
                  bgColor: CustomColors.lightGreyBackground,
                  startIcon: SvgPicture.asset('assets/icons/camera.svg'),
                  text: 'Open Camera',
                  action: () async {
                    File? imageFile =
                        await Navigator.pushNamed(context, Routes.scanDoc)
                            as File?;
                    if (imageFile != null) {
                      selectedFile = imageFile;
                      setState(() {});
                    }
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
