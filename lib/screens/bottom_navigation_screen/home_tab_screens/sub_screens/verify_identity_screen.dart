import 'package:agnostic_healthcare/common_widgets/app_card.dart';
import 'package:agnostic_healthcare/common_widgets/custom_app_bar.dart';
import 'package:agnostic_healthcare/common_widgets/profile_image_widget.dart';
import 'package:agnostic_healthcare/common_widgets/round_action_button.dart';
import 'package:agnostic_healthcare/constants/data_constants.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/utils/routes.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class VerifyIdentityScreen extends StatefulWidget {
  const VerifyIdentityScreen({super.key});

  @override
  State<VerifyIdentityScreen> createState() => _VerifyIdentityScreenState();
}

class _VerifyIdentityScreenState extends State<VerifyIdentityScreen> {
  bool showHowToVerify = false;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: Image.asset('assets/images/splash_bg.png', fit: BoxFit.fill),
        ),
        Scaffold(
          appBar: CustomAppBar(
            title: '',
            centerTitle: false,
            hideBackButton: true,
            titleWidget: Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Verify Your Identity',
                          style: AppTextStyle.styleSansSemiBold(20),
                        ),
                        Text(
                          'Unlock full features of the app.',
                          style: AppTextStyle.styleSansRegular(
                            12,
                            fontColor: CustomColors.textLight,
                          ),
                        ),
                      ],
                    ),
                  ),
                  ProfileImageWidget(imageSize: 56, imageUrl: profile),
                ],
              ),
            ),
          ),
          backgroundColor: Colors.transparent,
          body: Padding(
            padding: AppSpacing.leftRightSpacingLarge,
            child:
                showHowToVerify
                    ? Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      spacing: 14.spMin,
                      children: [
                        Text(
                          'How would you like to verify?',
                          style: AppTextStyle.styleSansSemiBold(18),
                        ),
                        AppSpacing.verticalXSpacing(space: 6),
                        AppCard(
                          bgColor: CustomColors.textWhite,
                          child: Column(
                            spacing: 13.spMin,
                            children: [
                              Row(
                                spacing: 12.spMin,
                                children: [
                                  Image.asset(
                                    'assets/icons/verify_healthcard.png',
                                  ),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Verify with Health Docs',
                                          style: AppTextStyle.styleSansBold(16),
                                        ),
                                        Text(
                                          'Use your photo ID and a recent prescription or lab result.',
                                          style: AppTextStyle.styleSansRegular(
                                            14,
                                            fontColor: CustomColors.textLight,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              RoundActionButton(
                                action:
                                    () => Navigator.pushReplacementNamed(
                                      context,
                                      Routes.verifyWithHealthCard,
                                    ),
                                height: 40,
                                text: 'Select',
                              ),
                            ],
                          ),
                        ),
                        Text(
                          'Or',
                          style: AppTextStyle.styleSansMedium(
                            16,
                            fontColor: CustomColors.textLight,
                          ),
                        ),
                        AppCard(
                          bgColor: CustomColors.textWhite,
                          child: Column(
                            spacing: 13.spMin,
                            children: [
                              Row(
                                spacing: 12.spMin,
                                children: [
                                  Image.asset('assets/icons/verify_gp.png'),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Verify with my GP Details',
                                          style: AppTextStyle.styleSansBold(16),
                                        ),
                                        Text(
                                          'Use your photo ID and GP details for a fast, secure match.',
                                          style: AppTextStyle.styleSansRegular(
                                            14,
                                            fontColor: CustomColors.textLight,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              RoundActionButton(
                                action:
                                    () => Navigator.pushReplacementNamed(
                                      context,
                                      Routes.verifyWithGp,
                                    ),
                                height: 40,
                                text: 'Select',
                              ),
                            ],
                          ),
                        ),
                      ],
                    )
                    : Column(
                      spacing: 30.spMin,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        AppCard(
                          bgColor: CustomColors.textWhite,
                          child: Column(
                            spacing: 13.spMin,
                            children: [
                              Row(
                                spacing: 12.spMin,
                                children: [
                                  Image.asset(
                                    'assets/icons/verify_itentity.png',
                                  ),
                                  Expanded(
                                    child: Text(
                                      'Verify your identity to see your full record and book appointments with your GP',
                                      style: AppTextStyle.styleSansSemiBold(16),
                                    ),
                                  ),
                                ],
                              ),
                              Text(
                                'For your security, we need to confirm your identity before you can access personal health information like GP appointments and prescriptions.',
                                style: AppTextStyle.styleSansRegular(
                                  14,
                                  fontColor: CustomColors.textLight,
                                ),
                              ),
                              RoundActionButton(
                                action: () {
                                  showHowToVerify = true;
                                  setState(() {});
                                },
                                text: 'Verify My Identity',
                              ),
                            ],
                          ),
                        ),
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: Text(
                            'Maybe Later',
                            style: AppTextStyle.styleSansMedium(
                              16,
                              fontColor: CustomColors.textLight,
                            ),
                          ),
                        ),
                      ],
                    ),
          ),
        ),
      ],
    );
  }
}
