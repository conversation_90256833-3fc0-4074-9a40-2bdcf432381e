import 'package:agnostic_healthcare/common_widgets/app_text_field.dart';
import 'package:agnostic_healthcare/common_widgets/custom_app_bar.dart';
import 'package:agnostic_healthcare/common_widgets/profile_image_widget.dart';
import 'package:agnostic_healthcare/constants/data_constants.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/screens/bottom_navigation_screen/providers_tab_screens/sub_widgets/clinic_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: Image.asset('assets/images/splash_bg.png', fit: BoxFit.fill),
        ),
        Scaffold(
          backgroundColor: Colors.transparent,
          appBar: CustomAppBar(
            title: '',
            centerTitle: false,
            titleWidget: Expanded(
              child: Row(
                spacing: 4.spMin,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  SvgPicture.asset(
                    'assets/icons/provider.svg',
                    height: 50.spMin,
                  ),
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Search',
                          style: AppTextStyle.styleSansSemiBold(20),
                        ),
                        Text(
                          'Find GPs and Book Appointments',
                          style: AppTextStyle.styleSansRegular(
                            12,
                            fontColor: CustomColors.textLight,
                          ),
                        ),
                      ],
                    ),
                  ),
                  ProfileImageWidget(imageSize: 56, imageUrl: profile),
                ],
              ),
            ),
          ),
          body: Padding(
            padding: AppSpacing.pagePaddingSmall,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 5.spMin,
              children: [
                AppTextField(
                  leadingWidget: SvgPicture.asset('assets/icons/search.svg'),
                  trailingWidget: SvgPicture.asset('assets/icons/filter.svg'),
                  hintText: 'Search Doctor. . .',
                ),
                SizedBox(),
                AppTextField(
                  leadingWidget: SvgPicture.asset(
                    'assets/icons/location_search.svg',
                  ),
                  trailingWidget: SvgPicture.asset(
                    'assets/icons/arrow_down_dark.svg',
                  ),
                  hintText: 'Location',
                ),
                Chip(
                  padding: EdgeInsets.symmetric(
                    horizontal: 18.spMin,
                    vertical: 6.spMin,
                  ),
                  side: BorderSide.none,
                  backgroundColor: CustomColors.textWhite,
                  deleteIcon: Icon(
                    Icons.cancel,
                    color: CustomColors.themeLight,
                  ),
                  onDeleted: () {},
                  shape: RoundedRectangleBorder(
                    borderRadius: AppRoundBorder.circularXpx(pixel: 50),
                  ),
                  label: Text(
                    'Auckland, NZ',
                    style: AppTextStyle.styleSansMedium(14),
                  ),
                ),
                SizedBox(),
                Row(
                  children: [
                    Text(
                      'Results',
                      style: AppTextStyle.styleSansMedium(
                        12,
                        fontColor: CustomColors.textLight,
                      ),
                    ),
                    Spacer(),
                    Icon(Icons.arrow_drop_down, color: CustomColors.textLight),
                    Text(
                      'A to Z',
                      style: AppTextStyle.styleSansMedium(
                        12,
                        fontColor: CustomColors.textLight,
                      ),
                    ),
                  ],
                ),

                Expanded(
                  child: ListView.separated(
                    shrinkWrap: true,
                    separatorBuilder:
                        (context, index) => AppSpacing.verticalXSpacing(),
                    itemCount: 12,
                    itemBuilder: (context, index) => ClinicCard(),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
