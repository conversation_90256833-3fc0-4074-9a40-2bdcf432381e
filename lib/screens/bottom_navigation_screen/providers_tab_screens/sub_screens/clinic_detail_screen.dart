import 'package:agnostic_healthcare/common_widgets/custom_app_bar.dart';
import 'package:agnostic_healthcare/common_widgets/icon_button.dart';
import 'package:agnostic_healthcare/common_widgets/round_action_button.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/screens/bottom_navigation_screen/providers_tab_screens/sub_widgets/practitioner_card.dart';
import 'package:agnostic_healthcare/utils/routes.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class ClinicDetailScreen extends StatefulWidget {
  const ClinicDetailScreen({super.key});

  @override
  State<ClinicDetailScreen> createState() => _ClinicDetailScreenState();
}

class _ClinicDetailScreenState extends State<ClinicDetailScreen> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              gradient: AppGradients.stoneBackgroundGradient,
            ),
          ),
        ),
        Positioned(
          top: kToolbarHeight + 50.spMin,
          child: SvgPicture.asset('assets/icons/clinic_detail_design.svg'),
        ),
        Scaffold(
          backgroundColor: Colors.transparent,
          appBar: CustomAppBar(
            leading: Padding(
              padding: EdgeInsets.symmetric(vertical: 10.spMin),
              child: Row(
                spacing: 20.spMin,
                children: [
                  AppIconButton(
                    iconAlignment: Alignment.centerLeft,
                    bgColor: Colors.transparent,
                    action: () => Navigator.pop(context),
                    icon: SvgPicture.asset(
                      'assets/icons/back.svg',
                      color: CustomColors.textWhite,
                    ),
                    padding: 0,
                    size: 24,
                  ),
                  Text(
                    'Practice Info',
                    style: AppTextStyle.styleSansMedium(
                      16,
                      fontColor: CustomColors.textWhite,
                    ),
                  ),
                ],
              ),
            ),
            title: '',
            titleColor: CustomColors.textWhite,
            centerTitle: false,
            actions: [
              AppIconButton(
                padding: 0,
                bgColor: Colors.transparent,
                size: 24.spMin,
                icon: SvgPicture.asset(
                  'assets/icons/search.svg',
                  color: CustomColors.textWhite,
                ),
              ),
              AppIconButton(
                padding: 0,
                bgColor: Colors.transparent,
                size: 24.spMin,
                icon: SvgPicture.asset(
                  'assets/icons/bookmark.svg',
                  color: CustomColors.textWhite,
                ),
              ),
            ],
          ),
          body: SingleChildScrollView(
            child: Column(
              spacing: 15.spMin,
              children: [
                Padding(
                  padding: AppSpacing.pagePaddingSmall,
                  child: Row(
                    spacing: 15.spMin,
                    children: [
                      Expanded(
                        child: Column(
                          spacing: 5.spMin,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Conrads Skin Clinic',
                              style: AppTextStyle.styleSansSemiBold(
                                20,
                                fontColor: CustomColors.textWhite,
                              ),
                            ),
                            Text(
                              '4 Practitioners',
                              style: AppTextStyle.styleSansMedium(
                                16,
                                fontColor: CustomColors.textWhite,
                              ),
                            ),
                            Text(
                              'Dermatologists',
                              style: AppTextStyle.styleSansRegular(
                                12,
                                fontColor: CustomColors.textWhite,
                              ),
                            ),
                            SizedBox(),
                            Row(
                              spacing: 10.spMin,
                              children: [
                                SvgPicture.asset(
                                  'assets/icons/location.svg',
                                  color: CustomColors.textWhite,
                                  width: 14.spMin,
                                ),
                                Text(
                                  '190M, Auckland, NZ',
                                  style: AppTextStyle.styleSansMedium(
                                    12,
                                    fontColor: CustomColors.textWhite,
                                  ),
                                ),
                              ],
                            ),
                            Row(
                              spacing: 10.spMin,
                              children: [
                                SvgPicture.asset(
                                  'assets/icons/money_white.svg',
                                  width: 14.spMin,
                                ),
                                Text(
                                  '\$70/Hour',
                                  style: AppTextStyle.styleSansMedium(
                                    12,
                                    fontColor: CustomColors.textWhite,
                                  ),
                                ),
                              ],
                            ),
                            Row(
                              spacing: 10.spMin,
                              children: [
                                SvgPicture.asset(
                                  'assets/icons/time.svg',
                                  color: CustomColors.textWhite,
                                  width: 14.spMin,
                                ),
                                Text(
                                  'Opened Until 5:00 PM Today ',
                                  style: AppTextStyle.styleSansMedium(
                                    12,
                                    fontColor: CustomColors.textWhite,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                        width: 130.spMin,
                        child: Image.asset('assets/images/clinic_profile.png'),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: double.infinity,
                  constraints: BoxConstraints(minHeight: 700.spMin),
                  padding: AppSpacing.pagePaddingSmall,
                  decoration: BoxDecoration(
                    gradient: AppGradients.splashBackgroundGradient,
                    borderRadius: AppRoundBorder.topRound30px,
                  ),
                  child: Column(
                    spacing: 5.spMin,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AppSpacing.verticalXSpacing(),
                      Padding(
                        padding: AppSpacing.leftRightSpacingSmall,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            infoColumn('experience', 'Experience', '15yr'),
                            infoColumn('treated', 'Treated', '500+'),
                            infoColumn('reviews', 'Reviews', '300+'),
                          ],
                        ),
                      ),
                      AppSpacing.verticalXSpacing(),
                      Text(
                        'GP/Clinic Details',
                        style: AppTextStyle.styleSansBold(18),
                      ),
                      SizedBox(),
                      Text.rich(
                        TextSpan(
                          children: [
                            TextSpan(
                              text:
                                  'Conrads Skin Clinic is a private medical centre. We pride ourselves on being friendly with a professional but relaxed atmosphere, in quiet surroundings. Our approach is to take care of our patients in a considered way, by spending the time needed with to help you manage your health concerns, whilst being mindful of the importance of running on time.',
                              style: AppTextStyle.styleSansRegular(
                                16,
                                fontColor: CustomColors.textLight,
                              ),
                            ),
                            TextSpan(
                              text: ' Read More',
                              style: AppTextStyle.styleSansSemiBold(
                                16,
                                fontColor: CustomColors.themeLight,
                              ),
                            ),
                          ],
                        ),
                      ),
                      AppSpacing.verticalXSpacing(),
                      Text(
                        'Practitioners (4)',
                        style: AppTextStyle.styleSansBold(18),
                      ),
                      SizedBox(
                        height: 200.spMin,
                        child: ListView.separated(
                          separatorBuilder:
                              (context, i) =>
                                  AppSpacing.horizontalXSpacing(space: 12),
                          scrollDirection: Axis.horizontal,
                          padding: EdgeInsets.symmetric(
                            vertical: 10.spMin,
                            horizontal: 2.spMin,
                          ),
                          itemCount: 5,
                          shrinkWrap: true,
                          itemBuilder: (context, index) => PractitionerCard(),
                        ),
                      ),
                      AppSpacing.verticalXSpacing(),
                      Text(
                        'Appointment Price',
                        style: AppTextStyle.styleSansBold(18),
                      ),
                      SizedBox(),
                      Text.rich(
                        TextSpan(
                          children: [
                            TextSpan(
                              text: 'Online: ',
                              style: AppTextStyle.styleSansMedium(
                                16,
                                fontColor: CustomColors.textLight,
                              ),
                            ),
                            TextSpan(
                              text: '\$70/hour',
                              style: AppTextStyle.styleSansSemiBold(
                                16,
                                fontColor: CustomColors.theme,
                              ),
                            ),
                            TextSpan(
                              text: '    In-Person: ',
                              style: AppTextStyle.styleSansMedium(
                                16,
                                fontColor: CustomColors.textLight,
                              ),
                            ),
                            TextSpan(
                              text: '\$100',
                              style: AppTextStyle.styleSansSemiBold(
                                16,
                                fontColor: CustomColors.theme,
                              ),
                            ),
                          ],
                        ),
                      ),
                      AppSpacing.verticalXSpacing(),
                      Text(
                        'Clinic Time',
                        style: AppTextStyle.styleSansBold(18),
                      ),
                      SizedBox(),
                      Text(
                        'Monday - Friday | 08.00 AM - 07:00 PM',
                        style: AppTextStyle.styleSansSemiBold(
                          16,
                          fontColor: CustomColors.textLight,
                        ),
                      ),
                      AppSpacing.verticalXSpacing(),
                      RoundActionButton(
                        text: 'Book Appointment',
                        action:
                            () => Navigator.pushNamed(
                              context,
                              Routes.bookPaidAppointment,
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget infoColumn(String iconName, String title, String time) => Column(
    spacing: 4.spMin,
    children: [
      SvgPicture.asset('assets/icons/$iconName.svg'),
      Text(time, style: AppTextStyle.styleSansBold(16)),
      Text(
        title,
        style: AppTextStyle.styleSansSemiBold(
          16,
          fontColor: CustomColors.textLight,
        ),
      ),
    ],
  );
}
