import 'package:agnostic_healthcare/common_widgets/app_card.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/utils/routes.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class ClinicCard extends StatelessWidget {
  final bool showBookmarkIcon;
  const ClinicCard({super.key, this.showBookmarkIcon = true});

  @override
  Widget build(BuildContext context) {
    return AppCard(
      action: () => Navigator.pushNamed(context, Routes.clinicDetail),
      bgColor: CustomColors.textWhite,
      child: Column(
        spacing: 4.spMin,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  'Conrads Skin Clinic | 4 Practitioners',
                  style: AppTextStyle.styleSansSemiBold(14),
                ),
              ),
              if (showBookmarkIcon)
                SvgPicture.asset('assets/icons/bookmark.svg'),
            ],
          ),
          Row(
            children: [
              Text(
                'Dermatologist',
                style: AppTextStyle.styleSansMedium(
                  14,
                  fontColor: CustomColors.textLight,
                ),
              ),
              AppSpacing.horizontalXSpacing(),
              SvgPicture.asset('assets/icons/location.svg'),
              Text(
                '190M, Auckland, NZ',
                style: AppTextStyle.styleSansMedium(
                  14,
                  fontColor: CustomColors.textLight,
                ),
              ),
            ],
          ),
          SizedBox(),
          Row(
            spacing: 4.spMin,
            children: [
              SvgPicture.asset('assets/icons/money.svg'),
              Text(
                '\$70/Hour',
                style: AppTextStyle.styleSansSemiBold(
                  14,
                  fontColor: CustomColors.themeLight,
                ),
              ),
              AppSpacing.horizontalXSpacing(),
              SvgPicture.asset('assets/icons/clock_colored.svg'),
              Text(
                'Today 5:00 PM',
                style: AppTextStyle.styleSansMedium(
                  14,
                  fontColor: CustomColors.yellow,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
