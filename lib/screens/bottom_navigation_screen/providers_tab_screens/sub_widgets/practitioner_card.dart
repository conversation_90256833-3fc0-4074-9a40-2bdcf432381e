import 'package:agnostic_healthcare/common_widgets/app_card.dart';
import 'package:agnostic_healthcare/common_widgets/round_action_button.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class PractitionerCard extends StatelessWidget {
  final bool showBookingButton;

  const PractitionerCard({super.key, this.showBookingButton = true});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 140.spMin,
      child: AppCard(
        elevation: 2,
        bgColor: CustomColors.textWhite,
        height: 180.spMin,
        child: Column(
          spacing: 5.spMin,
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset('assets/icons/profile_circled.svg'),
            SizedBox(),
            Text(
              '<PERSON><PERSON>',
              style: AppTextStyle.styleSansBold(14),
              textAlign: TextAlign.center,
            ),
            Text('Female GP', style: AppTextStyle.styleSansRegular(12)),
            if (showBookingButton) ...[
              SizedBox(),
              RoundActionButton(
                height: 36,
                text: 'Book',
                showGradiant: false,
                bgColor: CustomColors.textLight,
                elevation: 0,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
