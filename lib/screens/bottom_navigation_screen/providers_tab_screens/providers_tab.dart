import 'package:agnostic_healthcare/common_widgets/app_card.dart';
import 'package:agnostic_healthcare/common_widgets/app_text_field.dart';
import 'package:agnostic_healthcare/common_widgets/custom_app_bar.dart';
import 'package:agnostic_healthcare/common_widgets/profile_image_widget.dart';
import 'package:agnostic_healthcare/common_widgets/round_action_button.dart';
import 'package:agnostic_healthcare/constants/data_constants.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/screens/bottom_navigation_screen/my_gp_tab_screens/sub_widgets/appointment_card.dart';
import 'package:agnostic_healthcare/screens/bottom_navigation_screen/providers_tab_screens/sub_widgets/clinic_card.dart';
import 'package:agnostic_healthcare/utils/routes.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class ProvidersTab extends StatefulWidget {
  const ProvidersTab({super.key});

  @override
  State<ProvidersTab> createState() => _ProvidersTabState();
}

class _ProvidersTabState extends State<ProvidersTab> {
  late StateSetter dotsState;
  int dotsIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: Image.asset('assets/images/splash_bg.png', fit: BoxFit.fill),
        ),
        Scaffold(
          backgroundColor: Colors.transparent,
          appBar: CustomAppBar(
            title: '',
            centerTitle: false,
            titleWidget: Expanded(
              child: Row(
                spacing: 4.spMin,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  SvgPicture.asset(
                    'assets/icons/provider.svg',
                    height: 50.spMin,
                  ),
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Providers',
                          style: AppTextStyle.styleSansSemiBold(20),
                        ),
                        Text(
                          'Find GPs and Book Appointments',
                          style: AppTextStyle.styleSansRegular(
                            12,
                            fontColor: CustomColors.textLight,
                          ),
                        ),
                      ],
                    ),
                  ),
                  ProfileImageWidget(imageSize: 56, imageUrl: profile),
                ],
              ),
            ),
          ),
          body: SingleChildScrollView(
            padding: AppSpacing.pagePaddingSmall,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 15.spMin,
              children: [
                AppTextField(
                  leadingWidget: SvgPicture.asset('assets/icons/search.svg'),
                  trailingWidget: SvgPicture.asset('assets/icons/filter.svg'),
                  hintText: 'Search Doctor. . .',
                  isReadOnly: true,
                  onTap: () => Navigator.pushNamed(context, Routes.search),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Featured Services',
                      style: AppTextStyle.styleSansSemiBold(16),
                    ),
                    Text(
                      'See all',
                      style: AppTextStyle.styleSansSemiBold(
                        12,
                        fontColor: CustomColors.themeLight,
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 224.spMin,
                  child: PageView.builder(
                    onPageChanged: (index) {
                      dotsIndex = index;
                      dotsState(() {});
                    },
                    controller: PageController(viewportFraction: .95),
                    padEnds: false,
                    scrollDirection: Axis.horizontal,
                    itemCount: 10,
                    itemBuilder: (context, index) => docCard,
                  ),
                ),
                Center(
                  child: StatefulBuilder(
                    builder: (context, state) {
                      dotsState = state;
                      return Row(
                        spacing: 3.spMin,
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: List.generate(
                          10,
                          (index) => AnimatedContainer(
                            duration: Duration(milliseconds: 500),
                            curve: Curves.easeIn,
                            height: 7.spMin,
                            width: (index == dotsIndex ? 16 : 7).spMin,
                            decoration: BoxDecoration(
                              gradient:
                                  index == dotsIndex
                                      ? AppGradients.newKiwiButtonGradiant
                                      : null,
                              color:
                                  index == dotsIndex
                                      ? null
                                      : CustomColors.textSecondaryLight,
                              borderRadius: AppRoundBorder.circularXpx(
                                pixel: 4,
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'My Appointments',
                      style: AppTextStyle.styleSansSemiBold(16),
                    ),
                    InkWell(
                      onTap:
                          () => Navigator.pushNamed(
                            context,
                            Routes.myAppointments,
                          ),
                      child: Text(
                        'See all',
                        style: AppTextStyle.styleSansSemiBold(
                          12,
                          fontColor: CustomColors.themeLight,
                        ),
                      ),
                    ),
                  ],
                ),
                ...List.generate(2, (i) => AppointmentCard(upcoming: i == 0)),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Find Clinics',
                      style: AppTextStyle.styleSansSemiBold(16),
                    ),
                    Text(
                      'See all',
                      style: AppTextStyle.styleSansSemiBold(
                        12,
                        fontColor: CustomColors.themeLight,
                      ),
                    ),
                  ],
                ),
                ...List.generate(5, (i) => ClinicCard()),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget get docCard => Padding(
    padding: EdgeInsets.only(right: 10.spMin),
    child: AppCard(
      insetPadding: EdgeInsets.fromLTRB(12.spMin, 12.spMin, 0, 12.spMin),
      child: Stack(
        children: [
          Positioned(
            right: -0,
            top: 0,
            child: Image.asset('assets/images/doc_card_bg.png'),
          ),
          Padding(
            padding: EdgeInsets.only(right: 12.spMin),
            child: Column(
              spacing: 5.spMin,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  spacing: 5.spMin,
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 6.spMin),
                      decoration: BoxDecoration(
                        color: CustomColors.themeLight,
                        borderRadius: AppRoundBorder.circularXpx(pixel: 12),
                      ),
                      child: Text(
                        'Specialist',
                        style: AppTextStyle.styleSansMedium(
                          8,
                          fontColor: CustomColors.textWhite,
                        ),
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 6.spMin),
                      decoration: BoxDecoration(
                        color: CustomColors.themeLight,
                        borderRadius: AppRoundBorder.circularXpx(pixel: 12),
                      ),
                      child: Text(
                        '70\$/ Session',
                        style: AppTextStyle.styleSansMedium(
                          8,
                          fontColor: CustomColors.textWhite,
                        ),
                      ),
                    ),
                  ],
                ),
                Text(
                  'Dr Robert Finn',
                  style: AppTextStyle.styleSansSemiBold(
                    16,
                    fontColor: CustomColors.textWhite,
                  ),
                ),
                Row(
                  spacing: 5.spMin,
                  children: [
                    SvgPicture.asset('assets/icons/star.svg'),
                    Text(
                      '4.6 (432)',
                      style: AppTextStyle.styleSansMedium(
                        10,
                        fontColor: CustomColors.textWhite,
                      ),
                    ),
                    AppSpacing.horizontalXSpacing(),
                    SvgPicture.asset(
                      'assets/icons/location.svg',
                      color: CustomColors.textWhite,
                      height: 11.spMin,
                    ),
                    Text(
                      'Auckland, NZ',
                      style: AppTextStyle.styleSansMedium(
                        10,
                        fontColor: CustomColors.textWhite,
                      ),
                    ),
                  ],
                ),
                AppSpacing.verticalXSpacing(space: 5),
                Container(
                  padding: EdgeInsets.all(10.spMin),
                  decoration: BoxDecoration(
                    gradient: AppGradients.stoneBackgroundGradient.withOpacity(
                      .6,
                    ),
                    border: Border.all(color: CustomColors.textWhite),
                    borderRadius: AppRoundBorder.circularXpx(),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        spacing: 5.spMin,
                        children: [
                          Text(
                            'Availability',
                            style: AppTextStyle.styleSansMedium(
                              12,
                              fontColor: CustomColors.textWhite,
                            ),
                          ),
                          Spacer(),
                          Text(
                            '8 August 2025',
                            style: AppTextStyle.styleSansRegular(
                              11,
                              fontColor: CustomColors.textWhite,
                            ),
                          ),
                          SvgPicture.asset(
                            'assets/icons/arrow_down.svg',
                            color: CustomColors.textWhite,
                            height: 6.spMin,
                          ),
                        ],
                      ),
                      AppSpacing.verticalXSpacing(space: 10),
                      SingleChildScrollView(
                        padding: EdgeInsets.zero,
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          spacing: 4.spMin,
                          children: [
                            _buildDateItem(
                              day: 'MON',
                              date: '10',
                              isSelected: true,
                            ),
                            _buildDateItem(day: 'TUE', date: '11'),
                            _buildDateItem(day: 'WED', date: '12'),
                            _buildDateItem(day: 'THU', date: '13'),
                            _buildDateItem(day: 'FRI', date: '14'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(),
                RoundActionButton(
                  text: 'Book Meeting',
                  bgColor: CustomColors.textWhite,
                  showGradiant: false,
                  textColor: CustomColors.theme,
                  height: 35,
                  fontSize: 14,
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );

  Widget _buildDateItem({
    required String day,
    required String date,
    bool isSelected = false,
  }) {
    return Container(
      constraints: BoxConstraints(minWidth: 55.spMin),
      padding: EdgeInsets.all(3.spMin),
      decoration: BoxDecoration(
        color:
            isSelected
                ? CustomColors.textWhite.withValues(alpha: .48)
                : CustomColors.greyBackground.withValues(alpha: .39),
        borderRadius: BorderRadius.circular(50.0),
        border: Border.all(
          color: isSelected ? Colors.white : Colors.transparent,
        ),
      ),
      child: Row(
        spacing: 3.spMin,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            height: 21.spMin,
            width: 21.spMin,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isSelected ? CustomColors.textWhite : Colors.transparent,
              border: Border.all(color: CustomColors.textWhite),
            ),
            child: Center(
              child: Text(
                date,
                style: AppTextStyle.styleSansSemiBold(
                  8,
                  fontColor:
                      isSelected
                          ? CustomColors.themeLight
                          : CustomColors.textWhite,
                ),
              ),
            ),
          ),
          Text(
            day,
            style: AppTextStyle.styleSansSemiBold(
              10,
              fontColor: CustomColors.textWhite,
            ),
          ),
        ],
      ),
    );
  }
}
