import 'package:agnostic_healthcare/constants/data_constants.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/providers/providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../screens.dart';

class BottomNavigationScreen extends StatefulWidget {
  const BottomNavigationScreen({super.key});

  @override
  State<BottomNavigationScreen> createState() => _BottomNavigationScreenState();
}

class _BottomNavigationScreenState extends State<BottomNavigationScreen> {
  late UserProvider userProvider;
  late StateSetter bottomNavigationState;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    userProvider = Provider.of<UserProvider>(context);
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) {
        if (!didPop) {
          if (currentTabIndex != 0) {
            changeTab(0);
          }
        }
      },
      child: Scaffold(
        bottomNavigationBar: _buildBottomNavigationBar,
        body: PageView(
          physics: const NeverScrollableScrollPhysics(),
          controller: bottomNavigationTabController,
          onPageChanged: (index) => changeTab(index),
          children: const [
            RecordsTab(),
            MyGpTab(),
            HomeTab(),
            ProvidersTab(),
            AccountTab(),
          ],
        ),
      ),
    );
  }

  int currentTabIndex = 2;

  void changeTab(int index) {
    // if ((index == 0 || index == 1) && !userProvider.identityVerified) {
    //   return;
    // }
    if (currentTabIndex == index) {
      return;
    }
    currentTabIndex = index;
    bottomNavigationState(() {});
    if (bottomNavigationTabController.page != currentTabIndex) {
      bottomNavigationTabController.jumpToPage(currentTabIndex);
    }
  }

  Widget get _buildBottomNavigationBar => StatefulBuilder(
    builder: (context, state) {
      bottomNavigationState = state;
      return BottomAppBar(
        padding: EdgeInsets.zero,
        height: 86.spMin,
        elevation: 4,
        color: CustomColors.canvas,
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: _getBottomNavigationTabs,
        ),
      );
    },
  );

  List<Widget> get _getBottomNavigationTabs {
    Widget getIcon(
      String title,
      String asset, {
      bool disabled = false,
      bool isColored = false,
      int count = 0,
    }) => Container(
      width: 60.spMin,
      height: 60.spMin,
      padding: EdgeInsets.symmetric(vertical: 4.spMin),
      decoration: BoxDecoration(
        color: isColored ? CustomColors.theme : null,
        borderRadius: AppRoundBorder.circularXpx(pixel: 4),
      ),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          spacing: 4.spMin,
          children: [
            SvgPicture.asset(
              'assets/icons/$asset${disabled
                  ? '_disabled'
                  : isColored
                  ? '_filled'
                  : ''}.svg',
              height: 24.spMin,
            ),
            FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                title,
                textAlign: TextAlign.center,
                maxLines: 1,
                style: AppTextStyle.styleSansMedium(
                  12,
                  fontColor:
                      disabled
                          ? CustomColors.textLightGrey
                          : isColored
                          ? CustomColors.textWhite
                          : CustomColors.theme,
                ),
              ),
            ),
          ],
        ),
      ),
    );
    List<Widget> tabs = [];

    tabs.add(
      IconButton(
        icon: getIcon(
          'Record',
          'record',
          isColored: currentTabIndex == 0,
          // disabled: !userProvider.identityVerified,
        ),
        onPressed: () => changeTab(0),
      ),
    );
    tabs.add(
      IconButton(
        icon: getIcon(
          'My GP',
          'gp',
          isColored: currentTabIndex == 1,
          // disabled: !userProvider.identityVerified,
        ),
        onPressed: () => changeTab(1),
      ),
    );
    tabs.add(
      IconButton(
        icon: getIcon('Home', 'home', isColored: currentTabIndex == 2),
        onPressed: () => changeTab(2),
      ),
    );
    tabs.add(
      IconButton(
        icon: getIcon('Providers', 'provider', isColored: currentTabIndex == 3),
        onPressed: () => changeTab(3),
      ),
    );
    tabs.add(
      IconButton(
        icon: getIcon('Account', 'account', isColored: currentTabIndex == 4),
        onPressed: () => changeTab(4),
      ),
    );

    return tabs;
  }
}
