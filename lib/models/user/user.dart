class User {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String profileImage;
  final bool isEmailVerified;
  final bool isPhoneVerified;
  final bool isUserIdentityVerified;
  final DateTime createdAt;
  final DateTime? lastLoginAt;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.profileImage,
    required this.isEmailVerified,
    required this.isPhoneVerified,
    required this.isUserIdentityVerified,
    required this.createdAt,
    this.lastLoginAt,
  });

  // Computed property to check if user is fully verified
  bool get isFullyVerified => isEmailVerified && isPhoneVerified && isUserIdentityVerified;

  // Computed property to check if user has any verification
  bool get hasAnyVerification => isEmailVerified || isPhoneVerified || isUserIdentityVerified;

  // Copy with method for updating user data
  User copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? profileImage,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    bool? isUserIdentityVerified,
    DateTime? createdAt,
    DateTime? lastLoginAt,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      profileImage: profileImage ?? this.profileImage,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      isUserIdentityVerified: isUserIdentityVerified ?? this.isUserIdentityVerified,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
    );
  }

  // Factory method to create user from JSON
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      profileImage: json['profileImage'] ?? '',
      isEmailVerified: json['isEmailVerified'] ?? false,
      isPhoneVerified: json['isPhoneVerified'] ?? false,
      isUserIdentityVerified: json['isUserIdentityVerified'] ?? false,
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      lastLoginAt: json['lastLoginAt'] != null ? DateTime.parse(json['lastLoginAt']) : null,
    );
  }

  // Convert user to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'profileImage': profileImage,
      'isEmailVerified': isEmailVerified,
      'isPhoneVerified': isPhoneVerified,
      'isUserIdentityVerified': isUserIdentityVerified,
      'createdAt': createdAt.toIso8601String(),
      'lastLoginAt': lastLoginAt?.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'User(id: $id, name: $name, email: $email, isFullyVerified: $isFullyVerified)';
  }
}
