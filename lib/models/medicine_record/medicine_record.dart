class MedicineRecord {
  final String id;
  final String medicationName;
  final String dosage;
  final String frequency;
  final String prescribedBy;
  final DateTime prescribedDate;
  final DateTime? endDate;
  final String? instructions;
  final MedicationStatus status;
  final MedicationType type;

  MedicineRecord({
    required this.id,
    required this.medicationName,
    required this.dosage,
    required this.frequency,
    required this.prescribedBy,
    required this.prescribedDate,
    this.endDate,
    this.instructions,
    required this.status,
    required this.type,
  });

  // Copy with method for updating medicine record data
  MedicineRecord copyWith({
    String? id,
    String? medicationName,
    String? dosage,
    String? frequency,
    String? prescribedBy,
    DateTime? prescribedDate,
    DateTime? endDate,
    String? instructions,
    MedicationStatus? status,
    MedicationType? type,
  }) {
    return MedicineRecord(
      id: id ?? this.id,
      medicationName: medicationName ?? this.medicationName,
      dosage: dosage ?? this.dosage,
      frequency: frequency ?? this.frequency,
      prescribedBy: prescribedBy ?? this.prescribedBy,
      prescribedDate: prescribedDate ?? this.prescribedDate,
      endDate: endDate ?? this.endDate,
      instructions: instructions ?? this.instructions,
      status: status ?? this.status,
      type: type ?? this.type,
    );
  }

  // Factory method to create medicine record from JSON
  factory MedicineRecord.fromJson(Map<String, dynamic> json) {
    return MedicineRecord(
      id: json['id'] ?? '',
      medicationName: json['medicationName'] ?? '',
      dosage: json['dosage'] ?? '',
      frequency: json['frequency'] ?? '',
      prescribedBy: json['prescribedBy'] ?? '',
      prescribedDate: DateTime.parse(json['prescribedDate'] ?? DateTime.now().toIso8601String()),
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate']) : null,
      instructions: json['instructions'],
      status: MedicationStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => MedicationStatus.active,
      ),
      type: MedicationType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => MedicationType.prescription,
      ),
    );
  }

  // Convert medicine record to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'medicationName': medicationName,
      'dosage': dosage,
      'frequency': frequency,
      'prescribedBy': prescribedBy,
      'prescribedDate': prescribedDate.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'instructions': instructions,
      'status': status.toString().split('.').last,
      'type': type.toString().split('.').last,
    };
  }
}

enum MedicationStatus {
  active,
  completed,
  discontinued,
  paused,
}

enum MedicationType {
  prescription,
  overTheCounter,
  supplement,
  vaccine,
}
