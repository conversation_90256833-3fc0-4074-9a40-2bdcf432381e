class Appointment {
  final String id;
  final String doctorName;
  final String clinicName;
  final String specialty;
  final DateTime appointmentDate;
  final String timeSlot;
  final String location;
  final AppointmentStatus status;
  final AppointmentType type;
  final bool canJoin;
  final String? notes;

  Appointment({
    required this.id,
    required this.doctorName,
    required this.clinicName,
    required this.specialty,
    required this.appointmentDate,
    required this.timeSlot,
    required this.location,
    required this.status,
    required this.type,
    this.canJoin = false,
    this.notes,
  });

  // Copy with method for updating appointment data
  Appointment copyWith({
    String? id,
    String? doctorName,
    String? clinicName,
    String? specialty,
    DateTime? appointmentDate,
    String? timeSlot,
    String? location,
    AppointmentStatus? status,
    AppointmentType? type,
    bool? canJoin,
    String? notes,
  }) {
    return Appointment(
      id: id ?? this.id,
      doctorName: doctorName ?? this.doctorName,
      clinicName: clinicName ?? this.clinicName,
      specialty: specialty ?? this.specialty,
      appointmentDate: appointmentDate ?? this.appointmentDate,
      timeSlot: timeSlot ?? this.timeSlot,
      location: location ?? this.location,
      status: status ?? this.status,
      type: type ?? this.type,
      canJoin: canJoin ?? this.canJoin,
      notes: notes ?? this.notes,
    );
  }

  // Factory method to create appointment from JSON
  factory Appointment.fromJson(Map<String, dynamic> json) {
    return Appointment(
      id: json['id'] ?? '',
      doctorName: json['doctorName'] ?? '',
      clinicName: json['clinicName'] ?? '',
      specialty: json['specialty'] ?? '',
      appointmentDate: DateTime.parse(json['appointmentDate'] ?? DateTime.now().toIso8601String()),
      timeSlot: json['timeSlot'] ?? '',
      location: json['location'] ?? '',
      status: AppointmentStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => AppointmentStatus.scheduled,
      ),
      type: AppointmentType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => AppointmentType.inPerson,
      ),
      canJoin: json['canJoin'] ?? false,
      notes: json['notes'],
    );
  }

  // Convert appointment to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'doctorName': doctorName,
      'clinicName': clinicName,
      'specialty': specialty,
      'appointmentDate': appointmentDate.toIso8601String(),
      'timeSlot': timeSlot,
      'location': location,
      'status': status.toString().split('.').last,
      'type': type.toString().split('.').last,
      'canJoin': canJoin,
      'notes': notes,
    };
  }
}

enum AppointmentStatus {
  scheduled,
  confirmed,
  inProgress,
  completed,
  cancelled,
  rescheduled,
}

enum AppointmentType {
  inPerson,
  telehealth,
  phoneCall,
}
