import 'dart:io';

import 'package:agnostic_healthcare/constants/data_constants.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/providers/user_provider.dart';
import 'package:agnostic_healthcare/utils/shared_prefs.dart';
import 'package:agnostic_healthcare/utils/utils.dart';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await SharedPrefs().init();
  cameras = await availableCameras();
  // SharedPrefs().cleanPrefs();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      bottom: Platform.isAndroid,
      child: ScreenUtilInit(
        ensureScreenSize: true,
        designSize: <PERSON><PERSON>(375, 750),
        builder:
            (context, child) => MultiProvider(
              providers: [
                ChangeNotifierProvider(create: (_) => UserProvider()),
              ],
              child: child,
            ),
        child: MaterialApp(
          title: 'Agnostic Healthcare',
          navigatorKey: navigatorKey,
          debugShowCheckedModeBanner: false,
          // localizationsDelegates: context.localizationDelegates,
          // supportedLocales: context.supportedLocales,
          // locale: context.locale,
          darkTheme: ThemeData(
            brightness: Brightness.dark,
            canvasColor: CustomColors.canvas,
            scaffoldBackgroundColor: CustomColors.canvas,
            appBarTheme: AppBarTheme(
              shadowColor: Colors.transparent,
              surfaceTintColor: Colors.transparent,
              color: CustomColors.canvas,
              elevation: 0,
              // systemOverlayStyle: SystemUiOverlayStyle.dark.copyWith(
              //   statusBarColor: CustomColors.canvas,
              //   systemNavigationBarColor: CustomColors.canvas,
              // ),
            ),
          ),
          theme: ThemeData(
            brightness: Brightness.dark,
            canvasColor: CustomColors.canvas,
            scaffoldBackgroundColor: CustomColors.canvas,
            appBarTheme: AppBarTheme(
              shadowColor: Colors.transparent,
              surfaceTintColor: Colors.transparent,
              color: CustomColors.canvas,
              elevation: 0,
              // systemOverlayStyle: SystemUiOverlayStyle.dark.copyWith(
              //   statusBarColor: CustomColors.canvas,
              //   systemNavigationBarColor: CustomColors.canvas,
              // ),
            ),
          ),
          onGenerateRoute: RouteGenerator.generateRoute,
        ),
      ),
    );
  }
}
