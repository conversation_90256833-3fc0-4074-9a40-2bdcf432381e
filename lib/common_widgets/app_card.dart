import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AppCard extends StatelessWidget {
  final double? height;
  final Widget child;
  final Color? bgColor;
  final double? padding;
  final double? borderRadius;
  final Gradient? gradient;
  final double elevation;
  final BoxBorder? border;
  final Function()? action;
  final EdgeInsets? insetPadding;

  const AppCard({
    super.key,
    required this.child,
    this.bgColor,
    this.padding,
    this.height,
    this.gradient,
    this.borderRadius,
    this.action,
    this.elevation = 0,
    this.border,
    this.insetPadding,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: action,
      child: Material(
        borderRadius: AppRoundBorder.circularXpx(pixel: borderRadius ?? 14),
        elevation: elevation,
        child: Container(
          height: height?.spMin,
          width: double.infinity,
          padding: insetPadding ?? EdgeInsets.all((padding ?? 12).spMin),
          decoration: BoxDecoration(
            border: border,
            color: bgColor,
            gradient:
                bgColor != null
                    ? null
                    : gradient ?? AppGradients.stoneBackgroundGradient,
            borderRadius: AppRoundBorder.circularXpx(pixel: borderRadius ?? 14),
          ),
          child: child,
        ),
      ),
    );
  }
}
