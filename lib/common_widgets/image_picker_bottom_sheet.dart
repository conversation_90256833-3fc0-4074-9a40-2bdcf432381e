import 'dart:io';

import 'package:agnostic_healthcare/common_widgets/app_dialogs.dart';
import 'package:agnostic_healthcare/common_widgets/round_action_button.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/utils/general_methods.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';

void showPickImageBottomSheet(
  BuildContext context,
  Function(File) onImagePicked, {
  required Function(String) onError,
  double aspectRatio = 7 / 5,
}) => showCupertinoModalPopup(
  context: context,
  barrierDismissible: true,
  builder:
      (ctx) => CupertinoActionSheet(
        actions: [
          Container(
            color: CustomColors.lightBlackBackground,
            child: CupertinoActionSheetAction(
              onPressed: () => {},
              child: Text(
                'Select an option',
                style: AppTextStyle.styleSansRegular(16),
              ),
            ),
          ),
          const Divider(color: CustomColors.text, height: 0, thickness: 1),
          Column(
            children: [
              Container(
                color: CustomColors.lightBlackBackground,
                child: CupertinoActionSheetAction(
                  onPressed: () async {
                    Navigator.pop(context);
                    File? file = await GeneralMethods.pickSingleImage(
                      ImageSource.camera,
                      context,
                      aspectRatio: aspectRatio,
                    ).catchError((error) {
                      if (error.runtimeType == PlatformException &&
                          ((error as PlatformException).message?.contains(
                                'The user did not allow',
                              ) ??
                              false)) {
                        AppDialogs.showInfoDialog(
                          context: context,
                          titleIcon: 'camera',
                          detail: 'Camera Permission',
                          okButtonText: 'Open Settings',
                          okButtonFunction: () => openAppSettings(),
                        );
                      } else {
                        onError(
                          error.runtimeType == PlatformException
                              ? (error as PlatformException).message
                              : error,
                        );
                      }
                      return null;
                    });
                    if (file != null) onImagePicked(file);
                  },
                  child: Text('Camera', style: AppTextStyle.styleSansBold(16)),
                ),
              ),
              const Divider(color: CustomColors.text, height: 0, thickness: 1),
              Container(
                color: CustomColors.lightBlackBackground,
                child: CupertinoActionSheetAction(
                  onPressed: () async {
                    Navigator.pop(context);
                    File? file = await GeneralMethods.pickSingleImage(
                      ImageSource.gallery,
                      context,
                      aspectRatio: aspectRatio,
                    ).catchError((error) {
                      if (error.runtimeType == PlatformException &&
                          ((error as PlatformException).message?.contains(
                                'The user did not allow',
                              ) ??
                              false)) {
                        AppDialogs.showInfoDialog(
                          context: context,
                          titleIcon:
                              (error.message ?? '').toLowerCase().contains(
                                    'camera',
                                  )
                                  ? 'camera'
                                  : 'gallery',
                          detail:
                              (error.message ?? '').toLowerCase().contains(
                                    'camera',
                                  )
                                  ? 'Camera Permission'
                                  : 'Gallery Permission',
                          okButtonText: 'Open Settings',
                          okButtonFunction: () => openAppSettings(),
                        );
                      } else {
                        onError(
                          error.runtimeType == PlatformException
                              ? (error as PlatformException).message
                              : error,
                        );
                      }
                      return null;
                    });
                    if (file != null) onImagePicked(file);
                  },
                  child: Text('Gallery', style: AppTextStyle.styleSansBold(16)),
                ),
              ),
            ],
          ),
        ],
        cancelButton: RoundActionButton(
          text: 'Cancel',
          action: () => Navigator.pop(context),
        ),
      ),
);
