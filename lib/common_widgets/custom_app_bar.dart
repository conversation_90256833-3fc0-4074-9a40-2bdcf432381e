import 'package:agnostic_healthcare/common_widgets/icon_button.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final List<Widget>? actions;
  final Widget? leading;
  final Widget? titleWidget;
  final String title;
  final Function()? backButtonOverride;
  final bool hideBackButton;
  final bool centerTitle;
  final Color titleColor;
  final double? fontSize;

  const CustomAppBar({
    super.key,
    this.actions,
    required this.title,
    this.leading,
    this.backButtonOverride,
    this.titleWidget,
    this.hideBackButton = false,
    this.centerTitle = false,
    this.titleColor = CustomColors.text,
    this.fontSize,
  });

  @override
  Widget build(BuildContext context) {
    return PreferredSize(
      preferredSize: preferredSize,
      child: SafeArea(
        top: true,
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: 20.spMin,
            vertical: 10.spMin,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (leading != null)
                leading!
              else if (Navigator.canPop(context) && !hideBackButton)
                Center(
                  child: AppIconButton(
                    iconAlignment: Alignment.centerLeft,
                    bgColor: Colors.transparent,
                    action:
                        backButtonOverride != null
                            ? () => backButtonOverride!()
                            : () => Navigator.pop(context),
                    icon: SvgPicture.asset('assets/icons/back.svg'),
                    padding: 0.spMin,
                    size: 30,
                  ),
                ),
              if (titleWidget != null)
                titleWidget!
              else if (title.isNotEmpty)
                Text(
                  title,
                  style: AppTextStyle.styleSansRegular(
                    fontSize ?? 16,
                    fontColor: titleColor,
                  ),
                ),
              if (actions != null && actions!.isNotEmpty)
                Row(spacing: 15.spMin, children: actions!)
              else
                SizedBox(width: titleWidget != null ? 0 : 50.spMin),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight * 1.4);
}
