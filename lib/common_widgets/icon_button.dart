import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AppIconButton extends StatelessWidget {
  final Function()? action;
  final Widget icon;
  final double size;
  final Color? bgColor;
  final double borderRadius;
  final double padding;
  final Gradient? gradiant;
  final Alignment iconAlignment;

  const AppIconButton({
    super.key,
    this.action,
    required this.icon,
    this.borderRadius = 100,
    this.padding = 10,
    this.size = 50,
    this.bgColor,
    this.gradiant,
    this.iconAlignment = Alignment.center,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      radius: size * 1.4,
      onTap: action,
      child: Container(
        alignment: Alignment.center,
        height: size.spMin,
        width: size.spMin,
        decoration: BoxDecoration(
          gradient: gradiant,
          color: bgColor ?? CustomColors.textLight<PERSON>rey,
          borderRadius: AppRoundBorder.circularXpx(pixel: borderRadius),
        ),
        padding: EdgeInsets.all(padding.spMin),
        child: Align(alignment: iconAlignment, child: icon),
      ),
    );
  }
}
