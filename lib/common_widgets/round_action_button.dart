import 'package:agnostic_healthcare/common_widgets/circular_loader.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class RoundActionButton extends StatelessWidget {
  final VoidCallback? action;
  final double height, width;
  final String text;
  final Widget? endIcon;
  final Widget? startIcon;
  final double elevation;
  final Color? bgColor;
  final Color textColor;
  final BorderRadius? borderRadius;
  final double fontSize;
  final EdgeInsets? padding;
  final bool haveLowestWidth;
  final TextStyle? textStyle;
  final Border? border;
  final double? iconRightPadding;
  final bool showLoading;
  final bool showGradiant;

  const RoundActionButton({
    this.showGradiant = true,
    this.action,
    required this.text,
    this.height = 50,
    this.width = double.infinity,
    this.elevation = 2,
    this.startIcon,
    this.endIcon,
    this.bgColor,
    this.textColor = CustomColors.textWhite,
    this.fontSize = 16,
    this.padding,
    this.haveLowestWidth = false,
    super.key,
    this.borderRadius,
    this.textStyle,
    this.border,
    this.iconRightPadding,
    this.showLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    bool hasEndIcon = false, hasStartIcon = false;
    if (startIcon != null) {
      hasStartIcon = true;
    }
    if (endIcon != null) {
      hasEndIcon = true;
    }

    return SizedBox(
      width: haveLowestWidth ? null : width.spMin,
      height: height.spMin,
      // decoration: BoxDecoration(
      //   color: bgColor,
      //   border: border,
      //   borderRadius: borderRadius ?? AppRoundBorder.circularXpx(),
      //   gradient: AppGradients.newKiwiButtonGradiant,
      // ),
      child: ElevatedButton(
        onPressed: showLoading ? null : action,
        style: ButtonStyle(
          overlayColor: WidgetStateProperty.all(bgColor ?? Colors.transparent),
          backgroundColor: WidgetStatePropertyAll(
            bgColor ?? Colors.transparent,
          ),
          elevation: WidgetStateProperty.all(elevation),
          padding: WidgetStateProperty.all(EdgeInsets.zero),
          shape: WidgetStateProperty.all<RoundedRectangleBorder>(
            RoundedRectangleBorder(
              borderRadius: borderRadius ?? AppRoundBorder.circularXpx(),
            ),
          ),
        ),
        child: Container(
          width: haveLowestWidth ? null : width.spMin,
          height: height.spMin,
          padding: padding ?? AppSpacing.leftRightSpacingSmall,
          decoration: BoxDecoration(
            gradient: showGradiant ? AppGradients.buttonGradiant : null,
            // color: bgColor,
            borderRadius: borderRadius ?? AppRoundBorder.circularXpx(),
          ),
          child: Stack(
            children: [
              Center(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment:
                      hasStartIcon || !hasEndIcon
                          ? MainAxisAlignment.center
                          : MainAxisAlignment.start,
                  children: <Widget>[
                    if (hasStartIcon)
                      Padding(
                        padding: EdgeInsets.only(
                          right: (iconRightPadding ?? 8).spMin,
                        ),
                        child: startIcon!,
                      ),
                    if (text.isNotEmpty)
                      Text(
                        text,
                        textAlign: TextAlign.center,
                        style:
                            textStyle ??
                            AppTextStyle.styleSansBold(
                              fontSize,
                              fontColor: textColor,
                            ),
                      ),
                    if (hasEndIcon) Spacer(),
                    if (hasEndIcon) endIcon!,
                  ],
                ),
              ),
              if (showLoading)
                Positioned(
                  left: 0,
                  top: height / 2 - 10,
                  child: CircularLoader(size: 20.spMin, color: textColor),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
