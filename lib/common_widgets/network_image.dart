import 'package:agnostic_healthcare/common_widgets/circular_loader.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class AppNetworkImage extends StatelessWidget {
  final String url;
  final double imageHeight, imageWidth;
  final BorderRadius? borderRadius;
  final FilterQuality? quality;
  final Widget? child;

  const AppNetworkImage({
    super.key,
    required this.url,
    required this.imageHeight,
    required this.imageWidth,
    this.borderRadius,
    this.quality,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(minHeight: imageHeight.spMin),
      width: imageWidth.spMin,
      decoration: BoxDecoration(
        color: CustomColors.lightBlackBackground.withValues(alpha: 0.1),
        borderRadius: borderRadius ?? AppRoundBorder.circularXpx(),
      ),
      child: ClipRRect(
        borderRadius: borderRadius ?? AppRoundBorder.circularXpx(),
        child: ExtendedImage.network(
          url,
          fit: BoxFit.cover,
          height: imageHeight.spMin,
          width: imageWidth.spMin,
          filterQuality: quality ?? FilterQuality.high,
          loadStateChanged: (ExtendedImageState state) {
            switch (state.extendedImageLoadState) {
              case LoadState.loading:
                return const Center(child: CircularLoader());
              case LoadState.completed:
                if (state.extendedImageInfo?.image == null) {
                  return Center(
                    child:
                        child ??
                        SvgPicture.asset(
                          'assets/icons/profile.svg',
                          height: imageHeight / 4,
                        ),
                  );
                }
                return ExtendedRawImage(
                  filterQuality: quality ?? FilterQuality.high,
                  image: state.extendedImageInfo?.image,
                  width: imageWidth.spMin,
                  height: imageHeight.spMin,
                  fit: BoxFit.cover,
                );
              case LoadState.failed:
                return Center(
                  child:
                      child ??
                      SvgPicture.asset(
                        'assets/icons/profile.svg',
                        height: imageHeight / 4,
                      ),
                );
            }
          },
        ),
      ),
    );
  }
}
