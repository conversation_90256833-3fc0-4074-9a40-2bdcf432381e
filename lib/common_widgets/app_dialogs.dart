import 'package:agnostic_healthcare/common_widgets/circular_loader.dart';
import 'package:agnostic_healthcare/common_widgets/round_action_button.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class AppDialogs {
  static Future<void> showInfoDialog({
    required BuildContext context,
    required String titleIcon,
    String title = '',
    required String detail,
    String? okButtonText,
    bool showLoading = false,
    Function? okButtonFunction,
    Function? onCloseClicked,
  }) => showDialog(
    barrierDismissible: !showLoading,
    barrierColor: CustomColors.lightBlackBackground.withValues(alpha: 0.7),
    context: context,
    builder:
        (context) => Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: AppRoundBorder.circularXpx(pixel: 8),
          ),
          insetPadding: EdgeInsets.symmetric(horizontal: 25.spMin),
          backgroundColor: CustomColors.canvas,
          child: Padding(
            padding: EdgeInsets.all(20.spMin),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Align(
                  alignment: Alignment.centerRight,
                  child: InkWell(
                    onTap: () {
                      Navigator.pop(context);
                      if (onCloseClicked != null) {
                        onCloseClicked();
                      }
                    },
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    child: SizedBox(
                      height: 20.spMin,
                      width: 20.spMin,
                      child: Center(
                        child: SvgPicture.asset('assets/icons/close.svg'),
                      ),
                    ),
                  ),
                ),
                if (titleIcon.isNotEmpty)
                  SvgPicture.asset(
                    'assets/icons/$titleIcon.svg',
                    height: 94.spMin,
                    colorFilter:
                        titleIcon == 'error' || titleIcon == 'clock'
                            ? null
                            : ColorFilter.mode(
                              Color(0xffE5E7EB),
                              BlendMode.srcIn,
                            ),
                  ),
                if (title.isNotEmpty)
                  Text(title, style: AppTextStyle.styleSansRegular(18)),
                AppSpacing.verticalXSpacing(),
                Flexible(
                  child: SingleChildScrollView(
                    child: Text(
                      detail,
                      textAlign: TextAlign.center,
                      style: AppTextStyle.styleSansRegular(
                        14,
                        height: 1.5,
                        fontColor: CustomColors.text,
                      ),
                    ),
                  ),
                ),
                AppSpacing.verticalXSpacing(space: 20),
                if (showLoading)
                  SizedBox(height: 80.spMin, child: const CircularLoader())
                else
                  RoundActionButton(
                    action: () async {
                      Navigator.pop(context);
                      if (okButtonFunction != null) {
                        okButtonFunction();
                      }
                    },
                    text: okButtonText ?? 'Ok',
                  ),
              ],
            ),
          ),
        ),
  );

  static void showSimpleDialog({
    required BuildContext context,
    required String title,
    required String detail,
    required String okButtonText,
    String? cancelButtonText,
    Function? okButtonFunction,
    Function? cancelButtonFunction,
  }) => showDialog(
    context: context,
    builder:
        (ctx) => Dialog(
          backgroundColor: CustomColors.canvas,
          insetPadding: EdgeInsets.symmetric(horizontal: 24.spMin),
          shape: RoundedRectangleBorder(
            borderRadius: AppRoundBorder.circular20px,
          ),
          child: Padding(
            padding: EdgeInsets.all(16.spMin),
            child: Column(
              spacing: 32.spMin,
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Column(
                  spacing: 12.spMin,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Align(
                      alignment: Alignment.centerRight,
                      child: InkWell(
                        onTap: () => Navigator.pop(context),
                        splashColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        child: SizedBox(
                          height: 20.spMin,
                          width: 20.spMin,
                          child: Center(
                            child: SvgPicture.asset(
                              'assets/icons/close.svg',
                              colorFilter: ColorFilter.mode(
                                CustomColors.textLight,
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    Text(
                      title,
                      style: AppTextStyle.styleSansBold(22),
                      textAlign: TextAlign.center,
                    ),
                    Text(
                      detail,
                      textAlign: TextAlign.center,
                      style: AppTextStyle.styleSansRegular(
                        16,
                        fontColor: CustomColors.textLight,
                      ),
                    ),
                  ],
                ),
                Row(
                  spacing: 12.spMin,
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          Navigator.pop(context);
                          if (cancelButtonFunction != null) {
                            cancelButtonFunction();
                          }
                        },
                        child: RoundActionButton(
                          textStyle: AppTextStyle.styleSansBold(16),
                          text: cancelButtonText ?? 'cancel',
                        ),
                      ),
                    ),
                    Expanded(
                      child: RoundActionButton(
                        action: () async {
                          Navigator.pop(context);
                          if (okButtonFunction != null) {
                            okButtonFunction();
                          }
                        },
                        text: okButtonText,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
  );
}
