import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

import '../constants/data_constants.dart';

class ExpandableTextWidget extends StatefulWidget {
  const ExpandableTextWidget({super.key});

  @override
  State<ExpandableTextWidget> createState() => _ExpandableTextWidgetState();
}

class _ExpandableTextWidgetState extends State<ExpandableTextWidget> {
  bool showMore = false;

  @override
  Widget build(BuildContext context) {
    return Text.rich(
      TextSpan(
        children: [
          TextSpan(
            text: dummyText.substring(0, showMore ? null : 150),
            style: AppTextStyle.styleSansRegular(14),
          ),
          TextSpan(
            recognizer:
                TapGestureRecognizer()
                  ..onTap = () {
                    showMore = !showMore;
                    setState(() {});
                  },
            text: showMore ? ' Show less' : ' See More',
            style: AppTextStyle.styleSansRegular(
              14,
              fontColor: CustomColors.theme,
            ),
          ),
        ],
      ),
    );
  }
}
