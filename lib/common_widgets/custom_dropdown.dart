import 'package:agnostic_healthcare/constants/enums.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomDropdown<T> extends StatefulWidget {
  final List<T> options;
  final String? hint;
  final double? dropdownWidth;
  final Function(dynamic) onSelect;
  final double height, width;
  final T? selection;
  final Widget? icon;
  final double? elevation;
  final Color bgColor;
  final Color textColor;
  final BorderRadius? borderRadius;
  final double? fontSize;
  final EdgeInsets? padding, margin;
  final bool haveLowestWidth;
  final TextStyle? fontStyle;
  final String title;

  const CustomDropdown({
    required this.options,
    required this.onSelect,
    required this.selection,
    this.dropdownWidth,
    this.hint,
    super.key,
    this.height = 60,
    this.width = double.infinity,
    this.icon,
    this.elevation,
    this.bgColor = CustomColors.textWhite,
    this.textColor = CustomColors.text,
    this.borderRadius,
    this.fontSize,
    this.padding,
    this.fontStyle,
    this.haveLowestWidth = false,
    this.margin,
    this.title = '',
  });

  @override
  State<CustomDropdown> createState() => _CustomDropdownState();
}

class _CustomDropdownState<T> extends State<CustomDropdown> {
  T? selectedValue;

  @override
  void initState() {
    super.initState();
    selectedValue = widget.selection;
  }

  @override
  void didUpdateWidget(covariant CustomDropdown oldWidget) {
    super.didUpdateWidget(oldWidget);
    selectedValue = widget.selection;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.title.isNotEmpty)
          Padding(
            padding: EdgeInsets.only(bottom: 12.spMin),
            child: Text(widget.title, style: AppTextStyle.styleSansMedium(16)),
          ),
        Container(
          width: widget.haveLowestWidth ? null : widget.width.spMin,
          height: widget.height.spMin,
          margin: widget.margin ?? EdgeInsets.only(bottom: 7.spMin),
          decoration: BoxDecoration(
            color: widget.bgColor,
            borderRadius: AppRoundBorder.circularXpx(),
          ),
          padding: widget.padding ?? EdgeInsets.symmetric(horizontal: 10.spMin),
          child: DropdownButtonHideUnderline(
            child: DropdownButton2<T>(
              isExpanded: !widget.haveLowestWidth,
              buttonStyleData: CustomDropdownStyle.buttonStyleData,
              menuItemStyleData: CustomDropdownStyle.menuStyleData,
              dropdownStyleData: CustomDropdownStyle.dropdownStyleData(
                padding: EdgeInsets.symmetric(horizontal: 0.spMin),
              ),
              iconStyleData: CustomDropdownStyle.iconStyleDataSmall,
              hint: Text(
                widget.hint ?? '',
                style: AppTextStyle.styleSansRegular(
                  16,
                  fontColor: CustomColors.textLight,
                ),
              ),
              // isExpanded: true,
              value: selectedValue,
              style: AppTextStyle.styleSansMedium(
                widget.fontSize ?? 16,
                fontColor: widget.textColor,
              ),
              selectedItemBuilder:
                  (context) => createDropdownItems(
                    padding: 0,
                    isSelectedItemBuilder: true,
                  ),
              items: createDropdownItems(),
              onChanged: (T? value) {
                if (value != null) {
                  selectedValue = value;
                  setState(() {});
                  widget.onSelect(value);
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  List<DropdownMenuItem<T>> createDropdownItems({
    double padding = 10,
    bool isSelectedItemBuilder = false,
  }) {
    List<DropdownMenuItem<dynamic>> dropdownItems = [];
    for (dynamic item in widget.options) {
      if (item is Gender) {
        dropdownItems.add(
          DropdownMenuItem<Gender>(
            value: item,
            child: Text(
              item.title,
              style:
                  widget.fontStyle ??
                  AppTextStyle.styleSansMedium(
                    widget.fontSize ?? 16,
                    fontColor: widget.textColor,
                  ),
            ),
          ),
        );
      } else if (item is String) {
        dropdownItems.add(
          DropdownMenuItem<String>(
            value: item,
            child: Row(
              children: [
                if (widget.icon != null) widget.icon!,
                Text(
                  item,
                  style:
                      widget.fontStyle ??
                      AppTextStyle.styleSansMedium(
                        widget.fontSize ?? 16,
                        fontColor: widget.textColor,
                      ),
                ),
              ],
            ),
          ),
        );
      } else {
        dropdownItems.add(
          DropdownMenuItem<T>(
            value: item,
            child: Text(
              item.toString(),
              style:
                  widget.fontStyle ??
                  AppTextStyle.styleSansRegular(
                    widget.fontSize ?? 14,
                    fontColor: widget.textColor,
                  ),
            ),
          ),
        );
      }
    }
    return dropdownItems as List<DropdownMenuItem<T>>;
  }
}
