import 'dart:io';

import 'package:agnostic_healthcare/common_widgets/icon_button.dart';
import 'package:agnostic_healthcare/common_widgets/round_action_button.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/utils/general_methods.dart';
import 'package:circular_profile_avatar/circular_profile_avatar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_picker/image_picker.dart';

class ProfileImageWidget extends StatefulWidget {
  final String imageUrl;
  final double imageSize;
  final bool showGalleryPicker;
  final bool showDeleteIcon;
  final bool showGenerateButton;
  final Function(File?)? changeImageFunction;
  final Color borderColor;
  final double borderWidth;
  final Color? bgColor;
  final File? initialFile;
  final Widget? child;
  final Function()? generateFunction;

  const ProfileImageWidget({
    required this.imageSize,
    this.imageUrl = '',
    this.changeImageFunction,
    this.showGalleryPicker = false,
    this.bgColor,
    this.borderColor = CustomColors.theme,
    this.borderWidth = 3,
    super.key,
    this.initialFile,
    this.child,
    this.showGenerateButton = false,
    this.generateFunction,
    this.showDeleteIcon = false,
  });

  @override
  State<ProfileImageWidget> createState() => ProfileImageWidgetState();
}

class ProfileImageWidgetState extends State<ProfileImageWidget> {
  File? selectedImageFile;

  @override
  void initState() {
    super.initState();
    selectedImageFile = widget.initialFile;
  }

  @override
  void didUpdateWidget(covariant ProfileImageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    selectedImageFile = widget.initialFile;
  }

  @override
  Widget build(BuildContext context) {
    return !widget.showGalleryPicker && !widget.showDeleteIcon
        ? imageWidget
        : Column(
          spacing: 10.spMin,
          children: [
            imageWidget,
            Row(
              spacing: 10.spMin,
              mainAxisSize: MainAxisSize.min,
              children: [
                if (widget.showGenerateButton)
                  RoundActionButton(
                    action: widget.generateFunction,
                    text: 'Generate',
                    bgColor: CustomColors.theme.withValues(alpha: .2),
                    elevation: 0,
                    textColor: CustomColors.theme,
                    borderRadius: AppRoundBorder.circularXpx(pixel: 16),
                    width: 110,
                    height: 31,
                    startIcon: SvgPicture.asset('assets/icons/load.svg'),
                  ),
                if (widget.showGalleryPicker)
                  RoundActionButton(
                    action: () async {
                      File? file = await GeneralMethods.pickSingleImage(
                        ImageSource.gallery,
                        context,
                      );
                      if (file != null) {
                        selectedImageFile = file;
                        if (widget.changeImageFunction != null) {
                          widget.changeImageFunction!(file);
                        }
                        setState(() {});
                      }
                    },
                    text: 'Gallery',
                    bgColor: CustomColors.themeUltraLight.withValues(alpha: .2),
                    elevation: 0,
                    textColor: CustomColors.themeUltraLight,
                    borderRadius: AppRoundBorder.circularXpx(pixel: 16),
                    width: 110,
                    height: 31,
                    startIcon: SvgPicture.asset('assets/icons/gallery.svg'),
                  ),
              ],
            ),
          ],
        );
  }

  Widget get imageWidget => SizedBox(
    height: widget.imageSize.spMin,
    width: widget.imageSize.spMin,
    child: Stack(
      children: [
        CircularProfileAvatar(
          widget.imageUrl,
          cacheImage: true,
          errorWidget:
              (context, url, error) => Center(
                child:
                    widget.child ??
                    SvgPicture.asset(
                      'assets/icons/profile.svg',
                      height: (widget.imageSize * .9).spMin,
                    ),
              ),
          radius: (widget.imageSize * .9).spMin,
          backgroundColor: widget.bgColor ?? CustomColors.lightGreyBackground,
          borderWidth: widget.borderWidth,
          borderColor: widget.borderColor,
          child:
              selectedImageFile != null
                  ? Image.file(selectedImageFile!, fit: BoxFit.cover)
                  : widget.imageUrl.isNotEmpty
                  ? null
                  : widget.child ??
                      Center(
                        child: SvgPicture.asset(
                          'assets/icons/profile.svg',
                          height: (widget.imageSize * .9).spMin,
                        ),
                      ),
        ),
        if (widget.showDeleteIcon &&
            (selectedImageFile != null || widget.imageUrl.isNotEmpty))
          Align(
            alignment: Alignment.topRight,
            child: AppIconButton(
              action: () {
                selectedImageFile = null;
                setState(() {});
                if (widget.changeImageFunction != null) {
                  widget.changeImageFunction!(selectedImageFile);
                }
              },
              size: widget.imageSize / 4,
              icon: SvgPicture.asset('assets/icons/delete.svg'),
              padding: 3,
            ),
          ),
      ],
    ),
  );
}
