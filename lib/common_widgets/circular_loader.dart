import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:flutter/material.dart';

class CircularLoader extends StatelessWidget {
  final double? size, strokeWidth;
  final double? progress;
  final Color? color;

  const CircularLoader({
    super.key,
    this.size,
    this.strokeWidth,
    this.progress,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        height: size,
        width: size,
        child: CircularProgressIndicator(
          color: color ?? CustomColors.theme,
          strokeWidth: strokeWidth ?? 2,
          value: progress,
        ),
      ),
    );
  }
}
