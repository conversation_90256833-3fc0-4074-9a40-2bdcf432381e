import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class AppTextField extends StatefulWidget {
  final Widget? leadingWidget;
  final TextEditingController? textController;
  final Function(String)? onChange;
  final Function(String)? onSubmitted;
  final int? maxLength;
  final int maxLines;
  final int? minLines;
  final List<TextInputFormatter>? textInputFormatters;
  final TextInputType? inputType;
  final TextStyle? style, hintStyle, titleStyle;
  final TextInputAction? inputAction;
  final Function()? onTap;
  final Widget? trailingWidget;
  final bool isReadOnly;
  final Color? bgColor;
  final EdgeInsets? margin;
  final BorderRadius? borderRadius;
  final FocusNode? focusNode;
  final String? hintText;
  final bool placeEndIconOutside;
  final bool showHideTextIcon;
  final bool dontCloseKeyboard;
  final double? minHeight;
  final EdgeInsets? contentPadding;
  final bool? isDense;
  final bool autoFocus;
  final String error;
  final List<String>? autofillHints;
  final TextCapitalization? textCapitalization;
  final String title;
  final Border? border;
  final double? titleFont;
  final double? height;
  final Widget? bottomWidget;

  const AppTextField({
    super.key,
    this.autoFocus = false,
    this.dontCloseKeyboard = false,
    this.showHideTextIcon = false,
    this.margin,
    this.leadingWidget,
    this.textController,
    this.onChange,
    this.onSubmitted,
    this.maxLength,
    this.maxLines = 1,
    this.minLines,
    this.textInputFormatters,
    this.inputType,
    this.style,
    this.inputAction,
    this.onTap,
    this.trailingWidget,
    this.isReadOnly = false,
    this.bgColor,
    this.borderRadius,
    this.focusNode,
    this.placeEndIconOutside = false,
    this.hintText,
    this.minHeight,
    this.contentPadding,
    this.hintStyle,
    this.isDense,
    this.error = '',
    this.autofillHints,
    this.textCapitalization,
    this.title = '',
    this.border,
    this.titleFont,
    this.bottomWidget,
    this.titleStyle,
    this.height,
  });

  @override
  State<AppTextField> createState() => _AppTextFieldState();
}

class _AppTextFieldState extends State<AppTextField> {
  bool obscureText = false;

  @override
  void initState() {
    super.initState();
    if (widget.showHideTextIcon) {
      obscureText = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.title.isNotEmpty)
          Padding(
            padding: EdgeInsets.only(bottom: 12.spMin),
            child: Text(
              widget.title,
              style:
                  widget.titleStyle ??
                  AppTextStyle.styleSansMedium(
                    widget.titleFont ?? 16,
                    fontColor: CustomColors.textGrey,
                  ),
            ),
          ),
        StatefulBuilder(
          builder: (context, state) {
            return Container(
              height: widget.height,
              margin: widget.margin,
              decoration: BoxDecoration(
                borderRadius:
                    widget.borderRadius ?? AppRoundBorder.circularXpx(),
                color: widget.bgColor ?? CustomColors.lightBlackBackground,
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Flexible(
                        child: Container(
                          height: widget.height,
                          constraints: BoxConstraints(
                            minHeight:
                                widget.height ??
                                widget.minHeight?.spMin ??
                                48.spMin,
                          ),
                          decoration: BoxDecoration(
                            color: widget.bgColor ?? CustomColors.textWhite,
                            borderRadius:
                                widget.borderRadius ??
                                AppRoundBorder.circularXpx(),
                          ),
                          child: Row(
                            children: [
                              if (widget.leadingWidget != null)
                                AppSpacing.horizontalXSpacing(space: 10.spMin),
                              if (widget.leadingWidget != null)
                                widget.leadingWidget!,
                              Flexible(
                                child: TextField(
                                  enableSuggestions: true,
                                  autofillHints: widget.autofillHints ?? [],
                                  textCapitalization:
                                      widget.textCapitalization ??
                                      (widget.inputType !=
                                                  TextInputType.emailAddress &&
                                              !widget.showHideTextIcon
                                          ? TextCapitalization.sentences
                                          : TextCapitalization.none),
                                  onTapOutside:
                                      widget.dontCloseKeyboard
                                          ? null
                                          : (_) =>
                                              FocusManager.instance.primaryFocus
                                                  ?.unfocus(),
                                  // autocorrect: false,
                                  obscureText: obscureText,
                                  onTap: widget.onTap,
                                  focusNode: widget.focusNode,
                                  readOnly: widget.isReadOnly,
                                  autofocus: widget.autoFocus,
                                  controller: widget.textController,
                                  keyboardType: widget.inputType,
                                  maxLength: widget.maxLength,
                                  maxLines: widget.maxLines,
                                  minLines: widget.minLines,
                                  onChanged: widget.onChange,
                                  textInputAction: widget.inputAction,
                                  onSubmitted: widget.onSubmitted,
                                  inputFormatters: widget.textInputFormatters,
                                  style:
                                      widget.style ??
                                      AppTextStyle.styleSansRegular(14),
                                  decoration:
                                      AppInputDecoration.planeDecoration(
                                        isDense: widget.isDense,
                                        contentPadding:
                                            widget.contentPadding ??
                                            (widget.maxLines > 1
                                                ? EdgeInsets.all(10.spMin)
                                                : null),
                                        hintStyle: widget.hintStyle,
                                        hintText: widget.hintText,
                                      ),
                                ),
                              ),
                              if (widget.showHideTextIcon)
                                InkWell(
                                  splashColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                  onTap: () {
                                    obscureText = !obscureText;
                                    state(() {});
                                  },
                                  child: SvgPicture.asset(
                                    obscureText
                                        ? 'assets/icons/visibility.svg'
                                        : 'assets/icons/visibility_off.svg',
                                    color: CustomColors.theme,
                                  ),
                                ),
                              if (widget.trailingWidget != null &&
                                  !widget.placeEndIconOutside)
                                widget.trailingWidget!,
                              if ((widget.trailingWidget != null ||
                                      widget.showHideTextIcon) &&
                                  !widget.placeEndIconOutside)
                                AppSpacing.horizontalXSpacing(space: 7.spMin),
                            ],
                          ),
                        ),
                      ),
                      if (widget.trailingWidget != null &&
                          widget.placeEndIconOutside)
                        AppSpacing.horizontalXSpacing(space: 10.spMin),
                      if (widget.trailingWidget != null &&
                          widget.placeEndIconOutside)
                        widget.trailingWidget!,
                    ],
                  ),
                  if (widget.bottomWidget != null) widget.bottomWidget!,
                ],
              ),
            );
          },
        ),
        Align(
          alignment: Alignment.centerLeft,
          child: Visibility(
            visible: widget.error.isNotEmpty,
            child: Text(
              widget.error,
              style: AppTextStyle.styleSansRegular(
                12,
                fontColor: CustomColors.red,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
