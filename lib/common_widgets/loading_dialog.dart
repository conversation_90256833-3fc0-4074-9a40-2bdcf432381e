import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class LoadingDialog extends StatelessWidget {
  final String message;

  const LoadingDialog({super.key, this.message = 'Loading...'});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.zero),
      backgroundColor: Colors.transparent,
      elevation: 0, // No shadow for the dialog itself
      insetPadding: const EdgeInsets.all(0),
      child: Container(
        height: double.infinity,
        width: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              CustomColors.textWhite.withValues(alpha: .5),
              CustomColors.bgBlurColor.withValues(alpha: .5),
            ],
          ),
        ),
        child: Center(
          child: Container(
            decoration: BoxDecoration(
              color: CustomColors.textWhite,
              boxShadow: const [
                BoxShadow(
                  color: Color.fromRGBO(0, 0, 0, 0.13),
                  offset: Offset(0, 1),
                  blurRadius: 12.4,
                ),
              ],
              borderRadius: AppRoundBorder.circular20px,
            ),
            width: 209.spMin,
            height: 209.spMin,
            child: Column(
              spacing: 26.spMin,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(message, style: AppTextStyle.styleSansSemiBold(18)),
                SpinKitFadingCircle(size: 57.spMin, color: CustomColors.theme),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
