import 'package:agnostic_healthcare/common_screens/chat_screen.dart';
import 'package:agnostic_healthcare/common_widgets/app_text_field.dart';
import 'package:agnostic_healthcare/common_widgets/icon_button.dart';
import 'package:agnostic_healthcare/common_widgets/round_action_button.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/screens/bottom_navigation_screen/records_tab_screens/sub_widgets/immunization_card.dart';
import 'package:agnostic_healthcare/screens/bottom_navigation_screen/records_tab_screens/sub_widgets/medication_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CallScreen extends StatefulWidget {
  const CallScreen({super.key});

  @override
  State<CallScreen> createState() => _CallScreenState();
}

class _CallScreenState extends State<CallScreen> {
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        body: Stack(
          children: [
            Center(child: Image.asset('assets/images/call_bg.png')),
            Positioned(
              left: 20.spMin,
              top: kToolbarHeight,
              child: AppIconButton(
                size: 30,
                borderRadius: 10,
                bgColor: CustomColors.themeLight,
                padding: 10,
                icon: SvgPicture.asset('assets/icons/arrow_back.svg'),
                action: () => endCall,
              ),
            ),
            Align(
              alignment: Alignment.topCenter,
              child: SafeArea(
                child: Padding(
                  padding: EdgeInsets.only(top: 20.spMin),
                  child: Text(
                    'Video Call',
                    style: AppTextStyle.styleSansSemiBold(
                      20,
                      fontColor: CustomColors.textWhite,
                    ),
                  ),
                ),
              ),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: callInteractionButtons,
            ),
          ],
        ),
      ),
    );
  }

  Widget get _buildLocalUserWidget =>
      Image.asset('assets/images/call_fg.png', fit: BoxFit.cover);

  void get endCall {
    showDiagnosticBottomSheet;
    // Navigator.pop(context, true);
  }

  bool isMute = false;

  Widget get callInteractionButtons => StatefulBuilder(
    builder:
        (context, state) => SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisSize: MainAxisSize.min,
            children: [
              ClipRRect(
                borderRadius: AppRoundBorder.circularXpx(),
                child: Container(
                  margin: EdgeInsets.only(right: 33.spMin),
                  color: CustomColors.textWhite,
                  width: 100.spMin,
                  height: 180.spMin,
                  child: _buildLocalUserWidget,
                ),
              ),
              AppSpacing.verticalXSpacing(space: 21),
              Container(
                padding: AppSpacing.pagePaddingSmall,
                decoration: BoxDecoration(
                  color: CustomColors.textGrey.withValues(alpha: .78),
                ),
                child: Column(
                  spacing: 22.spMin,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            'Dr Eren Wesley',
                            style: AppTextStyle.styleSansBold(
                              18,
                              fontColor: CustomColors.textWhite,
                            ),
                          ),
                        ),
                        Text(
                          '5:24',
                          style: AppTextStyle.styleSansBold(
                            18,
                            fontColor: CustomColors.textWhite,
                          ),
                        ),
                      ],
                    ),
                    AppTextField(
                      isReadOnly: true,
                      onTap: () => showChatBottomSheet,
                      borderRadius: AppRoundBorder.circularXpx(pixel: 33),
                      hintText: 'Live Chat with Doctor',
                      hintStyle: AppTextStyle.styleSansRegular(
                        14,
                        fontColor: CustomColors.textLight,
                      ),
                      leadingWidget: AppIconButton(
                        size: 44,
                        bgColor: CustomColors.themeLight,
                        icon: SvgPicture.asset('assets/icons/chat.svg'),
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        iconButton(
                          'mic',
                          bgColor:
                              isMute
                                  ? CustomColors.red
                                  : CustomColors.themeLight,
                          action: () {
                            isMute = !isMute;
                            state(() {});
                          },
                        ),
                        iconButton('call_video', action: () {}),
                        iconButton('change_camera', action: () {}),
                        iconButton(
                          'hang_up',
                          action: () => endCall,
                          bgColor: CustomColors.red,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
  );

  Widget iconButton(
    String icon, {
    Color bgColor = CustomColors.themeLight,
    Function()? action,
  }) => GestureDetector(
    onTap: action,
    child: Container(
      height: 56.spMin,
      width: 56.spMin,
      decoration: BoxDecoration(color: bgColor, shape: BoxShape.circle),
      child: Center(child: SvgPicture.asset('assets/icons/$icon.svg')),
    ),
  );

  void get showChatBottomSheet => showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: CustomColors.canvas,
    constraints: BoxConstraints(maxHeight: 650.h),
    builder:
        (context) => Container(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: ChatScreen(),
        ),
  );

  void get showDiagnosticBottomSheet => showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    isDismissible: false,
    backgroundColor: CustomColors.canvas,
    constraints: BoxConstraints(maxHeight: 650.h),
    builder:
        (context) => PopScope(
          canPop: false,
          child: Padding(
            padding: AppSpacing.pagePaddingSmall,
            child: Column(
              spacing: 5.spMin,
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Text(
                    'Doctors Diagnosis',
                    style: AppTextStyle.styleSansBold(20),
                  ),
                ),
                AppSpacing.verticalXSpacing(),
                Text(
                  'Detailed Diagnosis',
                  style: AppTextStyle.styleSansSemiBold(18),
                ),
                Text(
                  'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc dictum a mauris Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc dictum a mauris ',
                  style: AppTextStyle.styleSansRegular(
                    16,
                    fontColor: CustomColors.textLight,
                  ),
                ),
                AppSpacing.verticalXSpacing(),
                Text(
                  'Treatment Recommendation',
                  style: AppTextStyle.styleSansSemiBold(18),
                ),
                MedicationCard(),
                SizedBox(),
                ImmunizationCard(),
                AppSpacing.verticalXSpacing(),
                RoundActionButton(
                  text: 'Done',
                  action:
                      () =>
                          Navigator.popUntil(context, (route) => route.isFirst),
                ),
              ],
            ),
          ),
        ),
  );
}
