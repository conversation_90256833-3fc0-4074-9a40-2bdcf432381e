import 'dart:io';

// ignore: depend_on_referenced_packages
import 'package:agnostic_healthcare/common_widgets/circular_loader.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ViewImage extends StatefulWidget {
  final String? url;
  final File? file;

  const ViewImage({super.key, this.url, this.file});

  @override
  State<ViewImage> createState() => _ViewImageState();
}

class _ViewImageState extends State<ViewImage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        automaticallyImplyLeading: false,
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Close',
              style: AppTextStyle.styleSansBold(
                14,
                fontColor: CustomColors.text,
              ),
            ),
          ),
          // AppSpacing.horizontalXSpacing(space: 10),
          // IconButton(
          //     onPressed: () => startDownload,
          //     icon: const Icon(Icons.download, color: CustomColors.themeColor)),
          AppSpacing.horizontalXSpacing(space: 10),
        ],
      ),
      body: Center(
        child:
            widget.url != null
                ? CachedNetworkImage(
                  fit: BoxFit.cover,
                  imageUrl: widget.url!,
                  progressIndicatorBuilder:
                      (context, url, downloadProgress) =>
                          const CircularLoader(),
                  errorWidget: (context, url, error) {
                    return SizedBox(
                      width: double.infinity,
                      child: Center(
                        child: Icon(
                          Icons.error,
                          color: CustomColors.textLight,
                          size: 20.spMin,
                        ),
                      ),
                    );
                  },
                )
                : widget.file != null
                ? Image.file(
                  widget.file!,
                  fit: BoxFit.contain,
                  width: double.infinity,
                )
                : SizedBox(),
      ),
    );
  }

  // void get startDownload async {
  //   String filename =
  //       widget.url.split("/")[widget.url.split("/").length - 1].split('.')[0];
  //   showSuccessMessage(
  //       message: 'Started downloading file....',
  //       context: navigatorKey.currentContext!);
  //   try {
  //     FlutterDownloader.enqueue(
  //         url: widget.url,
  //         headers: {},
  //         fileName: filename,
  //         saveInPublicStorage: true,
  //         allowCellular: true,
  //         savedDir: (await AppMethods.getDirectory())!.path,
  //         showNotification: true,
  //         openFileFromNotification: true);
  //   } catch (e) {
  //     debugPrint(e.toString());
  //   }
  // }
}
