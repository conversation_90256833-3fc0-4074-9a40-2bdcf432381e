import 'dart:io';

import 'package:agnostic_healthcare/common_widgets/circular_loader.dart';
import 'package:agnostic_healthcare/common_widgets/icon_button.dart';
import 'package:agnostic_healthcare/common_widgets/round_action_button.dart';
import 'package:agnostic_healthcare/constants/data_constants.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/utils/general_methods.dart';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ScanDocCamera extends StatefulWidget {
  const ScanDocCamera({super.key});

  @override
  State<ScanDocCamera> createState() => _ScanDocCameraState();
}

class _ScanDocCameraState extends State<ScanDocCamera>
    with WidgetsBindingObserver {
  File? imageFile;
  late CameraController controller;
  bool _isControllerInitialized = false;

  void showErrorMessage({required String error}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(error), duration: const Duration(seconds: 4)),
    );
  }

  Future<void> _initializeCameraController(
    CameraDescription cameraDescription,
  ) async {
    final CameraController cameraController = CameraController(
      cameraDescription,
      ResolutionPreset.medium,
      enableAudio: false,
      imageFormatGroup: ImageFormatGroup.jpeg,
    );

    controller = cameraController;

    cameraController.addListener(() {
      if (mounted) {
        setState(() {});
      }
      if (cameraController.value.hasError) {
        showErrorMessage(
          error: 'Camera error ${cameraController.value.errorDescription}',
        );
      }
    });

    try {
      await cameraController.initialize();
    } on CameraException catch (e) {
      switch (e.code) {
        case 'CameraAccessDenied':
          showErrorMessage(error: 'You have denied camera access.');
          break;
        case 'CameraAccessDeniedWithoutPrompt':
          showErrorMessage(
            error: 'Please go to Settings app to enable camera access.',
          );
          break;
        case 'CameraAccessRestricted':
          showErrorMessage(error: 'Camera access is restricted.');
          break;
        case 'AudioAccessDenied':
          showErrorMessage(error: 'You have denied audio access.');
          break;
        case 'AudioAccessDeniedWithoutPrompt':
          showErrorMessage(
            error: 'Please go to Settings app to enable audio access.',
          );
          break;
        case 'AudioAccessRestricted':
          showErrorMessage(error: 'Audio access is restricted.');
          break;
        default:
          showErrorMessage(error: e.toString());
      }
    }

    if (mounted) {
      setState(() {
        _isControllerInitialized = true;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    if (cameras.isNotEmpty) {
      _initializeCameraController(
        cameras.firstWhere(
          (e) => e.lensDirection == CameraLensDirection.back,
          orElse: () => cameras.first,
        ),
      );
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (!_isControllerInitialized) {
      return;
    }

    if (state == AppLifecycleState.inactive) {
      controller.dispose();
      setState(() {
        _isControllerInitialized = false;
      });
    } else if (state == AppLifecycleState.resumed) {
      _initializeCameraController(controller.description);
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isControllerInitialized) {
      return const Center(child: CircularLoader());
    }

    // Get the size of the screen
    final size = MediaQuery.of(context).size;

    return Scaffold(
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton:
          imageFile != null
              ? Padding(
                padding: AppSpacing.leftRightSpacingLarge,
                child: Column(
                  spacing: 20.spMin,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ClipRRect(
                      borderRadius: AppRoundBorder.circularXpx(pixel: 16),
                      child: Stack(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              color: CustomColors.textWhite,
                            ),
                            child: Row(
                              spacing: 17.spMin,
                              children: [
                                SizedBox(
                                  height: 80.spMin,
                                  width: 85.spMin,
                                  child: Image.file(
                                    imageFile!,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                                Expanded(
                                  child: Text(
                                    GeneralMethods.getFileNameFromPath(
                                      imageFile!.path,
                                    ),
                                    style: AppTextStyle.styleSansSemiBold(16),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Positioned(
                            right: 5.spMin,
                            top: 5.spMin,
                            child: AppIconButton(
                              action: () {
                                imageFile = null;
                                setState(() {});
                              },
                              size: 20,
                              padding: 0,
                              icon: Icon(
                                Icons.close,
                                color: CustomColors.text,
                                size: 15,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    RoundActionButton(
                      text: 'Next',
                      action: () => Navigator.pop(context, imageFile),
                    ),
                  ],
                ),
              )
              : Column(
                mainAxisSize: MainAxisSize.min,
                spacing: 20.spMin,
                children: [
                  Image.asset('assets/icons/user_id_card.png'),
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: AppRoundBorder.circularXpx(pixel: 100),
                      border: Border.all(
                        color: CustomColors.textWhite,
                        width: 2,
                      ),
                    ),
                    padding: EdgeInsets.all(5.spMin),
                    child: Container(
                      height: 80.spMin,
                      width: 80.spMin,
                      decoration: BoxDecoration(
                        borderRadius: AppRoundBorder.circularXpx(pixel: 100),
                        color: CustomColors.textWhite,
                      ),
                      child: IconButton(
                        icon: Icon(
                          Icons.camera_alt,
                          size: 40.spMin,
                          color: Colors.black,
                        ),
                        onPressed: () async {
                          try {
                            final XFile image = await controller.takePicture();
                            imageFile = File(image.path);
                            setState(() {});
                            // final directory =
                            //     await getApplicationDocumentsDirectory();
                            // final path =
                            //     '${directory.path}/${DateTime.now().millisecondsSinceEpoch}.jpg';
                            // await image.saveTo(path);
                            // showErrorMessage(error: 'Picture saved to $path');
                          } catch (e) {
                            print(e);
                            showErrorMessage(error: 'Failed to take picture');
                          }
                        },
                      ),
                    ),
                  ),
                ],
              ),
      body: Stack(
        children: [
          Positioned.fill(
            child: ColoredBox(color: CustomColors.lightBlackBackground),
          ),
          Center(child: CameraPreview(controller)),

          Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Padding(
                padding: EdgeInsets.only(top: 60.spMin),
                child: Text(
                  'Scan Document',
                  style: AppTextStyle.styleSansBold(
                    28,
                    fontColor: CustomColors.textWhite,
                  ),
                ),
              ),

              Expanded(
                child: Center(
                  child: CustomPaint(
                    painter: ScanBoxPainter(),
                    child: SizedBox(
                      width: 250.spMin, // Adjust as needed
                      height: 320.spMin, // Adjust as needed
                    ),
                  ),
                ),
              ),
              ColorFiltered(
                colorFilter: ColorFilter.mode(
                  Colors.black,
                  // CustomColors.bgBlurColor, // Adjust opacity as needed
                  BlendMode.dstOut,
                ),
                child: Container(
                  color: Colors.black, // A solid black background to filter
                ),
              ),
              // Empty space at the bottom to offset the floating button
              SizedBox(height: 120.spMin),
            ],
          ),
        ],
      ),
    );
  }
}

class ScanBoxPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = CustomColors.themeLight
          ..style = PaintingStyle.stroke
          ..strokeWidth = 6.0;

    final path = Path();

    // Top-left corner
    path.moveTo(size.width * 0.1, 0);
    path.lineTo(0, 0);
    path.lineTo(0, size.height * 0.1);

    // Top-right corner
    path.moveTo(size.width * 0.9, 0);
    path.lineTo(size.width, 0);
    path.lineTo(size.width, size.height * 0.1);

    // Bottom-right corner
    path.moveTo(size.width, size.height * 0.9);
    path.lineTo(size.width, size.height);
    path.lineTo(size.width * 0.9, size.height);

    // Bottom-left corner
    path.moveTo(size.width * 0.1, size.height);
    path.lineTo(0, size.height);
    path.lineTo(0, size.height * 0.9);

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
