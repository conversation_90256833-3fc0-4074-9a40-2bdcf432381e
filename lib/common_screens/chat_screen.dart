import 'package:agnostic_healthcare/common_widgets/app_text_field.dart';
import 'package:agnostic_healthcare/common_widgets/circular_loader.dart';
import 'package:agnostic_healthcare/common_widgets/icon_button.dart';
import 'package:agnostic_healthcare/common_widgets/profile_image_widget.dart';
import 'package:agnostic_healthcare/constants/data_constants.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:agnostic_healthcare/models/chat/chat_message.dart';
import 'package:agnostic_healthcare/utils/extensions.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:table_calendar/table_calendar.dart';

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  List<ChatMessage> get messages => [
    ChatMessage()
      ..createdAt = DateTime(2021, 02, 10, 10, 24)
      ..message = 'Good day!\nI need help with my test results'
      ..isMine = true,
    ChatMessage()
      ..createdAt = DateTime(2021, 02, 10, 10, 25)
      ..message = 'Here it is'
      ..url = dummyImages.first
      ..isMine = true,
    ChatMessage()
      ..createdAt = DateTime(2021, 02, 10, 10, 27)
      ..message = 'Hello, Andrew!\nJust give me 5 min, please',
  ];

  @override
  Widget build(BuildContext context) {
    return body;
  }

  Widget get body => Padding(
    padding: AppSpacing.pagePaddingSmall,
    child: Column(
      children: [
        Stack(
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: AppIconButton(
                iconAlignment: Alignment.centerLeft,
                bgColor: Colors.transparent,
                action: () => Navigator.pop(context),
                icon: SvgPicture.asset('assets/icons/back.svg'),
                padding: 0.spMin,
                size: 30,
              ),
            ),
            Align(
              alignment: Alignment.center,
              child: Row(
                spacing: 14.spMin,
                mainAxisSize: MainAxisSize.min,
                children: [
                  ProfileImageWidget(
                    imageSize: 40,
                    imageUrl: profile,
                    borderWidth: 0,
                  ),
                  Text('Live Chat', style: AppTextStyle.styleSansBold(20)),
                ],
              ),
            ),
          ],
        ),
        AppSpacing.verticalXSpacing(),
        Expanded(
          child: GroupedListView<ChatMessage, DateTime>(
            // controller: screenBloc.messageListController,
            elements: messages,
            groupBy: (message) => message.createdAt.toStartOfDay,
            order: GroupedListOrder.DESC,
            sort: true,
            reverse: true,
            floatingHeader: false,
            useStickyGroupSeparators: false,
            padding: EdgeInsets.zero,
            groupHeaderBuilder:
                (element) => Padding(
                  padding: EdgeInsets.symmetric(vertical: 20.spMin),
                  child: Center(
                    child: Text(
                      (isSameDay(element.createdAt.toStartOfDay, DateTime.now())
                              ? 'Today '
                              : element.createdAt.formatDateTime(
                                format: 'EEE ',
                              )) +
                          element.createdAt.formatDateTime(
                            format: 'dd MMM, yyyy',
                          ),
                      style: AppTextStyle.styleSansSemiBold(
                        12,
                        fontColor: CustomColors.textLight,
                      ),
                    ),
                  ),
                ),
            itemBuilder:
                (context, ChatMessage message) => chatBubble(message: message),
            itemComparator: (a, b) => a.createdAt.compareTo(b.createdAt),
          ),
        ),
        AppSpacing.verticalXSpacing(),
        messageTextField,
      ],
    ),
  );

  Widget chatBubble({required ChatMessage message}) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.spMin),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 12.spMin,
        children: [
          if (!message.isMine)
            ProfileImageWidget(
              imageUrl: profile,
              imageSize: 32,
              borderWidth: 0,
            ),
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(
                left: message.isMine ? 76.spMin : 0,
                right: message.isMine ? 0 : 76.spMin,
              ),
              child: Column(
                spacing: 4.spMin,
                crossAxisAlignment:
                    message.isMine
                        ? CrossAxisAlignment.end
                        : CrossAxisAlignment.start,
                children: [
                  if (message.message.isNotEmpty)
                    Container(
                      decoration: BoxDecoration(
                        color:
                            message.url.isNotEmpty
                                ? null
                                : (message.isMine
                                    ? CustomColors.textWhite
                                    : CustomColors.themeLight),
                        borderRadius:
                            message.isMine
                                ? AppRoundBorder.bottomRightSquareBorder
                                : AppRoundBorder.bottomLeftSquareBorder,
                      ),
                      padding: EdgeInsets.symmetric(
                        horizontal: 16.spMin,
                        vertical: 8.spMin,
                      ),
                      child: Text(
                        message.message,
                        style: AppTextStyle.styleSansRegular(
                          18,
                          fontColor:
                              message.url.isNotEmpty
                                  ? CustomColors.text
                                  : (message.isMine
                                      ? CustomColors.textLight
                                      : CustomColors.textWhite),
                        ),
                      ),
                    ),
                  if (message.url.isNotEmpty)
                    ClipRRect(
                      borderRadius:
                          message.isMine
                              ? AppRoundBorder.bottomRightSquareBorder
                              : AppRoundBorder.bottomLeftSquareBorder,
                      child: Container(
                        constraints: BoxConstraints(
                          minHeight: 80.spMin,
                          minWidth: 80.spMin,
                        ),
                        decoration: BoxDecoration(
                          color:
                              message.isMine
                                  ? CustomColors.textWhite
                                  : CustomColors.themeLight,
                          borderRadius:
                              message.isMine
                                  ? AppRoundBorder.bottomRightSquareBorder
                                  : AppRoundBorder.bottomLeftSquareBorder,
                        ),
                        child: CachedNetworkImage(
                          imageUrl: message.url,
                          progressIndicatorBuilder:
                              (context, url, downloadProgress) =>
                                  Center(child: CircularLoader()),
                          errorWidget: (context, url, error) {
                            print(error);
                            return Center(child: Icon(Icons.error));
                          },
                        ),
                      ),
                    ),

                  Row(
                    mainAxisSize: MainAxisSize.min,
                    spacing: 12.spMin,
                    children: [
                      if (message.isMine) ...[
                        SvgPicture.asset('assets/icons/is_read.svg'),
                        Text(
                          message.createdAt.formatDateTime(format: 'hh:mm aa'),
                          style: AppTextStyle.styleSansRegular(
                            14,
                            fontColor: CustomColors.textLight,
                          ),
                        ),
                      ],

                      if (!message.isMine)
                        Text(
                          message.createdAt.formatDateTime(format: 'hh:mm aa'),
                          style: AppTextStyle.styleSansRegular(
                            14,
                            fontColor: CustomColors.textLight,
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool sendingMessage = false;

  TextEditingController messageTextController = TextEditingController();

  String get message => messageTextController.text.trim();

  Widget get messageTextField {
    return StatefulBuilder(
      builder: (context, state) {
        return Row(
          spacing: 20.spMin,
          children: [
            Expanded(
              child: AppTextField(
                height: 44.spMin,
                minHeight: 44,
                contentPadding: EdgeInsets.all(5.spMin),
                isReadOnly: sendingMessage,
                textController: messageTextController,
                hintText: 'Type your message here',
                borderRadius: AppRoundBorder.circularXpx(pixel: 10),
                trailingWidget: SvgPicture.asset('assets/icons/attach.svg'),
              ),
            ),
            if (sendingMessage)
              SizedBox(
                height: 40.spMin,
                width: 40.spMin,
                child: Center(child: CircularProgressIndicator()),
              )
            else
              AppIconButton(
                size: 44,
                borderRadius: 10,
                bgColor: CustomColors.themeLight,
                icon: SvgPicture.asset('assets/icons/send.svg'),
                action: () {},
              ),
          ],
        );
      },
    );
  }
}
