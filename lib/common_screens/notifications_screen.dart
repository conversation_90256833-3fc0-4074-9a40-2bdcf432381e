import 'package:agnostic_healthcare/common_widgets/custom_app_bar.dart';
import 'package:agnostic_healthcare/constants/theme_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class NotificationsScreen extends StatelessWidget {
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: Image.asset('assets/images/splash_bg.png', fit: BoxFit.fill),
        ),
        Scaffold(
          backgroundColor: Colors.transparent,
          appBar: CustomAppBar(
            title: '',
            centerTitle: false,
            titleWidget: Expanded(
              child: Row(
                spacing: 4.spMin,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Notifications',
                        style: AppTextStyle.styleSansSemiBold(20),
                      ),
                      Text(
                        'Check all the app notifications',
                        style: AppTextStyle.styleSansRegular(
                          12,
                          fontColor: CustomColors.textLight,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          body: SingleChildScrollView(
            padding: AppSpacing.pagePaddingSmall,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 16.spMin,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('Today', style: AppTextStyle.styleSansSemiBold(16)),
                    Text(
                      'Mark all as read',
                      style: AppTextStyle.styleSansBold(
                        14,
                        fontColor: CustomColors.themeLight,
                      ),
                    ),
                  ],
                ),
                notificationTile(
                  title: 'Appointment Success',
                  detail:
                      'You have successfully booked your appointment with Dr. Emily Walker.',
                  iconName: 'calendar-tick',
                  time: '1 h',
                ),
                notificationTile(
                  title: 'Appointment Cancelled',
                  detail:
                      'You have successfully cancelled your appointment with Dr. David Patel.',
                  iconName: 'calendar-remove',
                  time: '2 h',
                ),
                notificationTile(
                  title: 'Scheduled Changed',
                  detail:
                      'You have successfully changes your appointment with Dr. Jesica Turner.',
                  iconName: 'calendar-edit',
                  time: '3 h',
                ),
                Text('YESTERDAY', style: AppTextStyle.styleSansSemiBold(16)),
                notificationTile(
                  title: 'Appointment Success',
                  detail:
                      'You have successfully booked your appointment with Dr. Emily Walker.',
                  iconName: 'calendar-tick',
                  time: '1 d',
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget notificationTile({
    required String title,
    required String detail,
    required String iconName,
    required String time,
  }) => Row(
    crossAxisAlignment: CrossAxisAlignment.start,
    spacing: 16.spMin,
    children: [
      Container(
        height: 60.spMin,
        width: 60.spMin,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: CustomColors.themeLight,
        ),
        child: Center(child: SvgPicture.asset('assets/icons/$iconName.svg')),
      ),
      Expanded(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 5.spMin,
          children: [
            Text(title, style: AppTextStyle.styleSansBold(16)),
            Text(
              detail,
              style: AppTextStyle.styleSansRegular(
                14,
                fontColor: CustomColors.textLight,
              ),
            ),
          ],
        ),
      ),
      Text(
        time,
        style: AppTextStyle.styleSansRegular(
          14,
          fontColor: CustomColors.textLight,
        ),
      ),
    ],
  );
}
