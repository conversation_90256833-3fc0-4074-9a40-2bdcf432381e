# Agnostic Healthcare - Flutter Application Documentation

## Table of Contents
1. [Project Overview](#project-overview)
2. [Architecture Overview](#architecture-overview)
3. [Project Structure](#project-structure)
4. [Application Flow](#application-flow)
5. [Key Components](#key-components)
6. [Data Models](#data-models)
7. [API Layer](#api-layer)
8. [State Management](#state-management)
9. [Navigation System](#navigation-system)
10. [UI Components](#ui-components)
11. [Constants and Theming](#constants-and-theming)
12. [Utilities](#utilities)
13. [Setup and Installation](#setup-and-installation)
14. [Development Guidelines](#development-guidelines)

## Project Overview

**Agnostic Healthcare** is a comprehensive Flutter mobile application designed for healthcare management. The app provides users with features for managing medical appointments, accessing healthcare providers, maintaining medical records, and communicating with healthcare professionals.

### Key Features
- **User Authentication**: Login/Signup with OTP verification
- **Healthcare Provider Directory**: Search and browse healthcare providers
- **Appointment Management**: Book and manage medical appointments
- **Medical Records**: Track medications, immunizations, and health data
- **Identity Verification**: Verify identity through GP details and health cards
- **Communication**: Chat and call functionality with healthcare providers
- **Document Scanning**: Camera integration for document capture

### Technology Stack
- **Framework**: Flutter 3.7.0+
- **State Management**: Provider pattern
- **HTTP Client**: Dio for API communication
- **UI**: Custom design system with responsive layouts
- **Storage**: SharedPreferences for local data persistence
- **Camera**: Integrated camera functionality for document scanning

## Architecture Overview

The application follows a **layered architecture** pattern with clear separation of concerns:

```
┌─────────────────────────────────────┐
│           Presentation Layer        │
│  (Screens, Widgets, UI Components)  │
├─────────────────────────────────────┤
│           Business Logic Layer      │
│     (Providers, State Management)   │
├─────────────────────────────────────┤
│              Data Layer             │
│    (API Services, Models, Utils)    │
├─────────────────────────────────────┤
│           Infrastructure Layer      │
│  (SharedPrefs, Constants, Routes)   │
└─────────────────────────────────────┘
```

### Design Patterns Used
- **Provider Pattern**: For state management and dependency injection
- **Repository Pattern**: For data access abstraction
- **Factory Pattern**: For route generation
- **Singleton Pattern**: For shared preferences and utilities

## Project Structure

```
lib/
├── api/                    # API layer and HTTP client configuration
│   ├── api_paths.dart     # API endpoint definitions
│   ├── dio.dart           # HTTP client configuration
│   └── healthcare_dio.dart # Healthcare-specific API services
├── common_screens/         # Shared screens across the app
│   ├── call_screen.dart   # Video/voice call interface
│   ├── chat_screen.dart   # Chat messaging interface
│   ├── notifications_screen.dart # Notifications display
│   ├── scan_doc_camera.dart # Document scanning camera
│   └── view_image_screen.dart # Image viewer
├── common_widgets/         # Reusable UI components
│   ├── app_card.dart      # Custom card component
│   ├── app_dialogs.dart   # Dialog components
│   ├── app_text_field.dart # Custom text input fields
│   ├── circular_loader.dart # Loading indicators
│   ├── custom_app_bar.dart # Custom app bar
│   ├── custom_dropdown.dart # Dropdown components
│   └── [other widgets...]
├── constants/              # Application constants
│   ├── data_constants.dart # Data and configuration constants
│   ├── enums.dart         # Enumeration definitions
│   └── theme_constants.dart # UI theme and styling constants
├── models/                 # Data models
│   ├── appointment/       # Appointment-related models
│   ├── chat/             # Chat message models
│   ├── medicine_record/  # Medical record models
│   └── models.dart       # Model exports
├── providers/              # State management providers
│   ├── user_provider.dart # User state management
│   └── providers.dart    # Provider exports
├── screens/               # Application screens
│   ├── appointment_booking_screens/ # Appointment booking flow
│   ├── bottom_navigation_screen/   # Main app navigation
│   ├── login_signup_screens/      # Authentication flow
│   ├── splash_screens/           # App initialization
│   └── screens.dart             # Screen exports
├── utils/                 # Utility functions and helpers
│   ├── extensions.dart   # Dart extensions
│   ├── general_methods.dart # Helper methods
│   ├── routes.dart       # Navigation routing
│   ├── shared_prefs.dart # Local storage utilities
│   └── utils.dart        # Utility exports
└── main.dart             # Application entry point
```

## Application Flow

### 1. Application Initialization
```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await SharedPrefs().init();           // Initialize local storage
  cameras = await availableCameras();   // Setup camera access
  runApp(const MyApp());
}
```

### 2. App Bootstrap Process
1. **Splash Screen**: Initial loading and app initialization
2. **Onboarding Check**: Determine if user has completed onboarding
3. **Authentication Check**: Verify if user is logged in
4. **Route to Appropriate Screen**: Navigate based on user state

### 3. Main Navigation Flow
The app uses a bottom navigation structure with 5 main tabs:
- **Records Tab** (Index 0): Medical records and health data
- **My GP Tab** (Index 1): General practitioner information
- **Home Tab** (Index 2): Dashboard and main features
- **Providers Tab** (Index 3): Healthcare provider directory
- **Account Tab** (Index 4): User profile and settings

### 4. Authentication Flow
```
Login Screen → OTP Verification → Identity Verification → Main App
     ↓              ↓                      ↓              ↓
  Phone/Email   → SMS/Email OTP  → GP/Health Card → Bottom Navigation
```

## Key Components

### 1. Main Application (main.dart)
The entry point configures:
- **Screen Utilities**: Responsive design setup
- **Provider Integration**: State management initialization
- **Theme Configuration**: Dark theme with custom colors
- **Route Generation**: Navigation system setup

### 2. Bottom Navigation System
Located in `screens/bottom_navigation_screen/`, this component:
- Manages tab switching with PageView
- Handles back button behavior
- Provides visual feedback for active tabs
- Supports conditional tab access based on verification status

### 3. Authentication System
Comprehensive authentication flow including:
- **Login/Signup**: Phone and email-based registration
- **OTP Verification**: SMS and email verification
- **Identity Verification**: GP details and health card verification
- **Secure Account**: Additional security measures

### 4. Provider Directory
Healthcare provider management system:
- **Search Functionality**: Find providers by location/specialty
- **Provider Details**: Comprehensive provider information
- **Appointment Booking**: Schedule appointments with providers
- **Clinic Information**: Detailed clinic and practitioner data

## Data Models

### Appointment Model
```dart
class Appointment {
  bool canJoin = false;
  // Additional appointment properties would be defined here
}
```

### Chat Message Model
```dart
class ChatMessage {
  DateTime createdAt = DateTime.now();
  String message = '';
  String url = '';
  bool isMine = false;
}
```

### Medicine Record Model
```dart
class MedicineRecord {
  // Medical record properties would be defined here
}
```

## API Layer

### HTTP Client Configuration (dio.dart)
The application uses Dio for HTTP communication with:
- **Base URL Configuration**: Centralized API endpoint management
- **Authentication Headers**: Automatic token injection
- **Error Handling**: Standardized error response processing
- **Timeout Management**: Request timeout configuration

### API Methods
- `postApiResponse()`: POST requests with form data support
- `getApiResponse()`: GET requests with query parameters
- `deleteApiResponse()`: DELETE requests with authentication

### API Endpoints (api_paths.dart)
Centralized endpoint definitions for:
- Authentication endpoints
- Profile management
- Healthcare provider data
- Appointment management
- Communication features

## State Management

### User Provider (user_provider.dart)
Manages user-related state:
```dart
class UserProvider with ChangeNotifier {
  bool identityVerified = false;
  
  void setVerification(bool status) {
    identityVerified = status;
    notifyListeners();
  }
  
  void login() {}
  void get logout => {};
}
```

### Provider Pattern Implementation
- **ChangeNotifier**: For reactive state updates
- **Consumer Widgets**: For UI updates based on state changes
- **Provider.of()**: For accessing provider data in widgets

## Navigation System

### Route Management (routes.dart)
Centralized route definitions with:
- **Static Route Constants**: Named route definitions
- **Route Generator**: Dynamic route creation
- **Parameter Passing**: Support for route arguments
- **Platform-Specific Navigation**: iOS/Android navigation differences

### Key Routes
- Authentication routes (login, signup, OTP)
- Main app routes (bottom navigation tabs)
- Feature-specific routes (appointments, providers)
- Utility routes (camera, image viewer)

## UI Components

### Custom Widgets
The app includes a comprehensive set of reusable components:

#### AppCard
```dart
class AppCard extends StatelessWidget {
  final Widget child;
  final Color? bgColor;
  final double? padding;
  final Gradient? gradient;
  // Provides consistent card styling across the app
}
```

#### Custom App Bar
Standardized app bar with:
- Consistent styling
- Back button handling
- Title customization
- Action button support

#### Form Components
- **AppTextField**: Custom text input with validation
- **CustomDropdown**: Styled dropdown components
- **RoundActionButton**: Primary action buttons

### Design System
- **Consistent Spacing**: Standardized spacing values
- **Color Palette**: Defined color scheme
- **Typography**: Custom font families and text styles
- **Border Radius**: Consistent corner radius values

## Constants and Theming

### Theme Constants (theme_constants.dart)
Comprehensive theming system including:

#### Color Palette
```dart
class CustomColors {
  static const Color theme = Color(0xFF73A127);        // Primary green
  static const Color canvas = Color(0xFFF4F4F2);       // Background
  static const Color text = Color(0xff262626);         // Primary text
  // Additional color definitions...
}
```

#### Text Styles
- Font families: DMSans variants (Regular, Medium, Bold, etc.)
- Responsive text sizing with ScreenUtil
- Consistent text styling across components

#### Component Styling
- Dropdown styles
- Button styles
- Card styles
- Calendar styles

### Data Constants (data_constants.dart)
Application-wide constants:
- Global variables (cameras, shared preferences)
- Profile image assets
- Configuration values

## Utilities

### Shared Preferences (shared_prefs.dart)
Local storage management:
```dart
class SharedPrefs {
  String get savedUserToken => // Get stored auth token
  void setUserToken(String token) => // Store auth token
  bool get onboardingComplete => // Check onboarding status
  // Additional preference methods...
}
```

### General Methods (general_methods.dart)
Helper functions for:
- Data type conversions
- Date/time formatting
- Validation utilities
- Common operations

### Extensions (extensions.dart)
Dart extensions for enhanced functionality:
- Widget extensions
- String extensions
- DateTime extensions
- Collection extensions

## Setup and Installation

### Prerequisites
- Flutter SDK 3.7.0 or higher
- Dart SDK
- Android Studio / VS Code
- iOS development tools (for iOS deployment)

### Installation Steps
1. **Clone the repository**
2. **Install dependencies**: `flutter pub get`
3. **Configure API endpoints** in `api_paths.dart`
4. **Set up platform-specific configurations**
5. **Run the application**: `flutter run`

### Dependencies
Key packages used:
- `provider`: State management
- `dio`: HTTP client
- `flutter_screenutil`: Responsive design
- `shared_preferences`: Local storage
- `camera`: Camera functionality
- `image_picker`: Image selection
- `flutter_svg`: SVG support

## Development Guidelines

### Code Organization
- Follow the established folder structure
- Use barrel exports (index files) for clean imports
- Implement proper separation of concerns
- Maintain consistent naming conventions

### State Management Best Practices
- Use Provider for app-wide state
- Implement proper disposal of resources
- Avoid unnecessary rebuilds with Consumer widgets
- Keep business logic separate from UI components

### API Integration
- Use the established Dio configuration
- Implement proper error handling
- Add loading states for API calls
- Cache data when appropriate

### UI Development
- Use the custom design system components
- Implement responsive design with ScreenUtil
- Follow the established theming system
- Ensure accessibility compliance

### Testing Strategy
- Write unit tests for business logic
- Implement widget tests for UI components
- Add integration tests for critical user flows
- Test on multiple devices and screen sizes

---

This documentation provides a comprehensive overview of the Agnostic Healthcare Flutter application. For specific implementation details, refer to the individual source files and their inline documentation.
