# Agnostic Healthcare - Flutter Application

A comprehensive healthcare management mobile application built with Flutter, providing users with seamless access to healthcare services, appointment booking, medical records management, and provider communication.

## 🏥 Overview

Agnostic Healthcare is a modern, user-friendly mobile application designed to bridge the gap between patients and healthcare providers. The app offers a complete healthcare ecosystem with features for appointment management, medical records tracking, provider discovery, and secure communication.

### ✨ Key Features

- **🔐 Secure Authentication**: Multi-factor authentication with OTP verification
- **👨‍⚕️ Provider Directory**: Comprehensive healthcare provider search and discovery
- **📅 Appointment Management**: Easy booking and management of medical appointments
- **📋 Medical Records**: Digital storage and management of health records
- **💬 Communication**: Secure chat and video calling with healthcare providers
- **🆔 Identity Verification**: Robust identity verification through GP and health card validation
- **📱 Responsive Design**: Optimized for all screen sizes and devices
- **📄 Document Scanning**: Integrated camera functionality for document capture

## 🚀 Quick Start

### Prerequisites
- Flutter SDK 3.7.0+
- Dart SDK
- Android Studio / VS Code
- iOS development tools (for iOS deployment)

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd agnostic_healthcare

# Install dependencies
flutter pub get

# Run the application
flutter run
```

For detailed setup instructions, see [Development Setup Guide](docs/DEVELOPMENT_SETUP.md).

## 📚 Documentation

### Core Documentation
- **[Complete Documentation](DOCUMENTATION.md)** - Comprehensive project overview
- **[Development Setup](docs/DEVELOPMENT_SETUP.md)** - Environment setup and configuration
- **[API Documentation](docs/API_DOCUMENTATION.md)** - API integration and usage
- **[UI Components Guide](docs/UI_COMPONENTS_GUIDE.md)** - Custom widget documentation
- **[Navigation Flow](docs/NAVIGATION_FLOW.md)** - App navigation and routing
- **[State Management](docs/STATE_MANAGEMENT.md)** - Provider pattern implementation

### Architecture Overview
```
┌─────────────────────────────────────┐
│           Presentation Layer        │
│  (Screens, Widgets, UI Components)  │
├─────────────────────────────────────┤
│           Business Logic Layer      │
│     (Providers, State Management)   │
├─────────────────────────────────────┤
│              Data Layer             │
│    (API Services, Models, Utils)    │
├─────────────────────────────────────┤
│           Infrastructure Layer      │
│  (SharedPrefs, Constants, Routes)   │
└─────────────────────────────────────┘
```

## 🏗️ Project Structure

```
lib/
├── api/                    # API layer and HTTP client
├── common_screens/         # Shared screens
├── common_widgets/         # Reusable UI components
├── constants/              # App constants and theming
├── models/                 # Data models
├── providers/              # State management
├── screens/               # Application screens
├── utils/                 # Utilities and helpers
└── main.dart             # Application entry point
```

## 🛠️ Technology Stack

- **Framework**: Flutter 3.7.0+
- **Language**: Dart
- **State Management**: Provider Pattern
- **HTTP Client**: Dio
- **Local Storage**: SharedPreferences
- **UI Framework**: Material Design with custom components
- **Camera Integration**: Camera plugin
- **Image Handling**: Image picker and processing
- **Responsive Design**: ScreenUtil

## 📱 Application Flow

### Authentication Flow
```
Splash → Onboarding → Login → OTP → Identity Verification → Main App
```

### Main Navigation
The app features a bottom navigation with 5 main sections:
- **Records**: Medical records and health data
- **My GP**: General practitioner information
- **Home**: Dashboard and main features (default)
- **Providers**: Healthcare provider directory
- **Account**: User profile and settings

## 🎨 Design System

### Color Palette
- **Primary**: `#73A127` (Healthcare Green)
- **Background**: `#F4F4F2` (Light Canvas)
- **Text**: `#262626` (Dark Gray)
- **Accent**: `#8CC331` (Light Green)

### Typography
- **Font Family**: DM Sans (Regular, Medium, Bold, SemiBold, ExtraLight)
- **Responsive Sizing**: ScreenUtil integration
- **Consistent Hierarchy**: Standardized text styles

## 🔧 Development

### Running the App
```bash
# Development mode
flutter run --debug

# Release mode
flutter run --release

# With environment variables
flutter run --dart-define=ENVIRONMENT=development
```

### Testing
```bash
# Run all tests
flutter test

# Run with coverage
flutter test --coverage

# Widget tests
flutter test test/widget/

# Integration tests
flutter test integration_test/
```

### Building
```bash
# Android APK
flutter build apk --release

# iOS
flutter build ios --release

# App Bundle (Play Store)
flutter build appbundle --release
```

## 🔐 Security Features

- **Token-based Authentication**: Secure JWT token management
- **Data Encryption**: Sensitive data encryption
- **Secure Storage**: Protected local data storage
- **API Security**: HTTPS enforcement and proper error handling
- **Input Validation**: Comprehensive input sanitization

## 🌐 API Integration

The app integrates with healthcare APIs for:
- User authentication and profile management
- Healthcare provider data
- Appointment booking and management
- Medical records synchronization
- Communication services

See [API Documentation](docs/API_DOCUMENTATION.md) for detailed integration guide.

## 🧪 Testing Strategy

- **Unit Tests**: Business logic and provider testing
- **Widget Tests**: UI component testing
- **Integration Tests**: End-to-end user flow testing
- **Performance Tests**: Memory and performance profiling

## 📈 Performance Optimization

- **Lazy Loading**: Efficient data loading strategies
- **Image Caching**: Optimized image handling
- **State Management**: Efficient provider usage
- **Bundle Optimization**: Minimized app size

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow the established code style and conventions
- Write comprehensive tests for new features
- Update documentation for significant changes
- Ensure all tests pass before submitting PR

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation for common solutions

## 🔄 Version History

- **v1.0.0+5**: Current version with core healthcare features
- See [CHANGELOG.md](CHANGELOG.md) for detailed version history

---

**Built with ❤️ using Flutter**
