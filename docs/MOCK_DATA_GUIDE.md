# Mock Data Implementation Guide

## Overview
This guide explains the mock data system implemented for the Agnostic Healthcare app, supporting different user verification states and demonstrating the conditional UI behavior based on user verification levels.

## User Verification System

### Three-Tier Verification
The app supports three levels of user verification:

1. **`isEmailVerified`** - Email address verification status
2. **`isPhoneVerified`** - Phone number verification status  
3. **`isUserIdentityVerified`** - Identity document verification status

### User Types

#### 1. Fully Verified User
- ✅ Email Verified
- ✅ Phone Verified  
- ✅ Identity Verified
- **Home Screen**: Shows upcoming appointments
- **Data Access**: Full access to appointments and medical records

#### 2. Partially Verified User
- ✅ Email Verified
- ✅ Phone Verified
- ❌ Identity Verified
- **Home Screen**: Shows "Unlock your medical records" message
- **Data Access**: Limited access until identity verification

#### 3. Unverified User
- ❌ Email Verified
- ❌ Phone Verified
- ❌ Identity Verified
- **Home Screen**: Shows "Unlock your medical records" message
- **Data Access**: No access to sensitive medical data

## Mock Data Structure

### Users (`lib/constants/mock_data.dart`)
```dart
// Fully verified user - shows appointments
MockData.verifiedUser

// Partially verified user - shows unlock message
MockData.partiallyVerifiedUser  

// Unverified user - shows unlock message
MockData.unverifiedUser
```

### Appointments
- **Dr. Aaron Smith** - Cardiologist (Telehealth, can join)
- **Dr. Emily Wilson** - Dermatologist (In-person)
- **Dr. James Taylor** - General Practitioner (In-person)

### Medicine Records
- **Paracetamol** - 500mg, twice daily
- **Vitamin D3** - 1000 IU, once daily
- **Ibuprofen** - 200mg, as needed

## Testing Different User Types

### Method 1: Home Screen Configuration
In `lib/screens/bottom_navigation_screen/home_tab_screens/home_tab.dart`, line 37:

```dart
// Change this value to test different scenarios:
userProvider.loginWithMockUser(isVerified: true);  // Verified user
userProvider.loginWithMockUser(isVerified: false); // Unverified user
```

### Method 2: Using Test Helper
```dart
import 'package:agnostic_healthcare/utils/user_test_helper.dart';

// Test different scenarios
UserTestHelper.loginAsVerifiedUser(userProvider);
UserTestHelper.loginAsUnverifiedUser(userProvider);
UserTestHelper.loginAsPartiallyVerifiedUser(userProvider);
```

### Method 3: Custom Test Scenarios
```dart
UserTestHelper.runTestScenario(userProvider, TestScenario.fullyVerified);
UserTestHelper.runTestScenario(userProvider, TestScenario.unverified);
```

## UI Behavior Based on Verification

### Verified Users (`isFullyVerified = true`)
- **Upcoming Appointments Section**: Shows real appointment data
- **Recent Records Section**: Shows medicine records
- **Navigation**: "See all" links work for appointments and medications
- **Actions**: Can join telehealth appointments

### Unverified Users (`isFullyVerified = false`)
- **Home Screen**: Shows "Unlock Your Medical Records" card instead of appointments
- **No Appointments**: Empty appointment list
- **No Records**: Empty medicine records list
- **Call to Action**: Prompts user to complete verification

## Key Files Modified

### Models
- `lib/models/user/user.dart` - Enhanced user model with verification flags
- `lib/models/appointment/appointment.dart` - Complete appointment model
- `lib/models/medicine_record/medicine_record.dart` - Complete medicine record model

### Providers
- `lib/providers/user_provider.dart` - Enhanced with verification state management

### Mock Data
- `lib/constants/mock_data.dart` - Comprehensive mock data for all scenarios

### UI Updates
- `lib/screens/bottom_navigation_screen/home_tab_screens/home_tab.dart` - Conditional rendering based on verification

### Utilities
- `lib/utils/user_test_helper.dart` - Testing utilities for different user scenarios

## Design Preservation

✅ **All existing design elements preserved:**
- Color scheme and theming unchanged
- Typography and spacing maintained
- Existing UI components reused
- Gradient and styling consistency
- Animation and interaction patterns preserved

## Development Notes

### Adding New Mock Data
1. Add new entries to `MockData` class in `lib/constants/mock_data.dart`
2. Follow existing patterns for appointments and medicine records
3. Ensure data relationships are logical (verified users get data, unverified don't)

### Testing Verification Flows
1. Use `UserTestHelper` for quick scenario testing
2. Modify `home_tab.dart` initialization for persistent testing
3. Check `UserProvider` methods for verification state management

### Extending Verification Logic
1. Add new verification types to `User` model
2. Update `UserProvider` with new verification methods
3. Implement UI logic in relevant screens
4. Add corresponding mock data scenarios

## Future Enhancements

- **Real API Integration**: Replace mock data with actual API calls
- **Verification Workflows**: Implement step-by-step verification processes
- **Progressive Disclosure**: Show different content based on verification level
- **Verification Badges**: Visual indicators of verification status
- **Onboarding Flows**: Guide users through verification process
