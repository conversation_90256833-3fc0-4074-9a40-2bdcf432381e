# Development Setup Guide - Agnostic Healthcare

## Prerequisites

### System Requirements
- **Operating System**: macOS, Windows, or Linux
- **RAM**: Minimum 8GB, recommended 16GB
- **Storage**: At least 10GB free space
- **Internet**: Stable internet connection for package downloads

### Required Software

#### 1. Flutter SDK
- **Version**: 3.7.0 or higher
- **Installation**: Follow [Flutter installation guide](https://docs.flutter.dev/get-started/install)
- **Verification**: Run `flutter doctor` to verify installation

#### 2. Dart SDK
- **Version**: Included with Flutter SDK
- **Verification**: Run `dart --version`

#### 3. IDE/Editor
Choose one of the following:
- **Android Studio** (Recommended)
  - Version: Latest stable
  - Plugins: Flutter, Dart
- **Visual Studio Code**
  - Extensions: Flutter, Dart, Flutter Widget Snippets
- **IntelliJ IDEA**
  - Plugins: Flutter, Dart

#### 4. Platform-Specific Tools

##### For Android Development
- **Android Studio**: Latest stable version
- **Android SDK**: API level 21 or higher
- **Android Emulator** or physical device
- **Java Development Kit (JDK)**: Version 11 or higher

##### For iOS Development (macOS only)
- **Xcode**: Latest stable version
- **iOS Simulator** or physical device
- **CocoaPods**: `sudo gem install cocoapods`

## Project Setup

### 1. Clone Repository
```bash
git clone <repository-url>
cd agnostic_healthcare
```

### 2. Install Dependencies
```bash
# Install Flutter dependencies
flutter pub get

# For iOS (macOS only)
cd ios
pod install
cd ..
```

### 3. Environment Configuration

#### Create Environment Files
Create the following files in the project root:

**`.env.development`**
```
API_BASE_URL=https://dev-api.agnostichealthcare.com/
API_TIMEOUT=30000
DEBUG_MODE=true
```

**`.env.production`**
```
API_BASE_URL=https://api.agnostichealthcare.com/
API_TIMEOUT=30000
DEBUG_MODE=false
```

#### Configure API Endpoints
Update `lib/api/api_paths.dart` with your API endpoints:
```dart
class ApiPaths {
  static const String baseUrl = String.fromEnvironment(
    'API_BASE_URL',
    defaultValue: 'https://dev-api.agnostichealthcare.com/',
  );
  
  // Add your API endpoints here
}
```

### 4. Platform Configuration

#### Android Configuration
1. **Update Package Name** (if needed):
   ```bash
   flutter pub run change_app_package_name:main com.yourcompany.agnostic_healthcare
   ```

2. **Configure Permissions** in `android/app/src/main/AndroidManifest.xml`:
   ```xml
   <uses-permission android:name="android.permission.INTERNET"/>
   <uses-permission android:name="android.permission.CAMERA"/>
   <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
   <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
   ```

3. **Update Minimum SDK** in `android/app/build.gradle`:
   ```gradle
   android {
       compileSdkVersion 34
       defaultConfig {
           minSdkVersion 21
           targetSdkVersion 34
       }
   }
   ```

#### iOS Configuration
1. **Update Bundle Identifier** in `ios/Runner.xcodeproj`

2. **Configure Permissions** in `ios/Runner/Info.plist`:
   ```xml
   <key>NSCameraUsageDescription</key>
   <string>This app needs camera access to scan documents</string>
   <key>NSPhotoLibraryUsageDescription</key>
   <string>This app needs photo library access to select images</string>
   ```

3. **Set Minimum iOS Version** in `ios/Podfile`:
   ```ruby
   platform :ios, '12.0'
   ```

## Development Environment

### 1. IDE Setup

#### Android Studio
1. Install Flutter and Dart plugins
2. Configure Flutter SDK path
3. Set up device/emulator
4. Configure code style and formatting

#### VS Code
1. Install recommended extensions:
   ```json
   {
     "recommendations": [
       "dart-code.flutter",
       "dart-code.dart-code",
       "alexisvt.flutter-snippets",
       "nash.awesome-flutter-snippets"
     ]
   }
   ```

2. Configure settings in `.vscode/settings.json`:
   ```json
   {
     "dart.flutterSdkPath": "/path/to/flutter",
     "editor.formatOnSave": true,
     "dart.lineLength": 80,
     "editor.rulers": [80]
   }
   ```

### 2. Code Formatting
Configure automatic code formatting:

**`.vscode/settings.json`**
```json
{
  "editor.formatOnSave": true,
  "dart.lineLength": 80,
  "[dart]": {
    "editor.formatOnSave": true,
    "editor.selectionHighlight": false,
    "editor.suggest.snippetsPreventQuickSuggestions": false,
    "editor.suggestSelection": "first",
    "editor.tabCompletion": "onlySnippets",
    "editor.wordBasedSuggestions": false
  }
}
```

### 3. Debugging Configuration

#### Launch Configuration
Create `.vscode/launch.json`:
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Flutter (Development)",
      "request": "launch",
      "type": "dart",
      "args": ["--dart-define=ENVIRONMENT=development"]
    },
    {
      "name": "Flutter (Production)",
      "request": "launch",
      "type": "dart",
      "args": ["--dart-define=ENVIRONMENT=production"]
    }
  ]
}
```

## Running the Application

### 1. Check Flutter Installation
```bash
flutter doctor
```

### 2. List Available Devices
```bash
flutter devices
```

### 3. Run Application
```bash
# Run on default device
flutter run

# Run on specific device
flutter run -d <device-id>

# Run in debug mode
flutter run --debug

# Run in release mode
flutter run --release

# Run with environment variables
flutter run --dart-define=ENVIRONMENT=development
```

### 4. Hot Reload
- Press `r` in terminal for hot reload
- Press `R` for hot restart
- Press `q` to quit

## Testing Setup

### 1. Unit Testing
```bash
# Run all tests
flutter test

# Run specific test file
flutter test test/unit/user_provider_test.dart

# Run tests with coverage
flutter test --coverage
```

### 2. Widget Testing
```bash
# Run widget tests
flutter test test/widget/

# Run with verbose output
flutter test --verbose
```

### 3. Integration Testing
```bash
# Run integration tests
flutter test integration_test/
```

## Build Configuration

### 1. Android Build
```bash
# Debug APK
flutter build apk --debug

# Release APK
flutter build apk --release

# App Bundle (for Play Store)
flutter build appbundle --release
```

### 2. iOS Build
```bash
# Debug build
flutter build ios --debug

# Release build
flutter build ios --release

# Archive for App Store
flutter build ipa --release
```

## Common Issues and Solutions

### 1. Flutter Doctor Issues
```bash
# Fix Android license issues
flutter doctor --android-licenses

# Update Flutter
flutter upgrade

# Clean and get packages
flutter clean
flutter pub get
```

### 2. iOS Build Issues
```bash
# Clean iOS build
cd ios
rm -rf Pods
rm Podfile.lock
pod install
cd ..
flutter clean
flutter build ios
```

### 3. Android Build Issues
```bash
# Clean Android build
cd android
./gradlew clean
cd ..
flutter clean
flutter build apk
```

### 4. Package Conflicts
```bash
# Clear pub cache
flutter pub cache repair

# Reset packages
rm pubspec.lock
flutter pub get
```

## Development Workflow

### 1. Git Workflow
```bash
# Create feature branch
git checkout -b feature/new-feature

# Make changes and commit
git add .
git commit -m "Add new feature"

# Push to remote
git push origin feature/new-feature

# Create pull request
```

### 2. Code Review Checklist
- [ ] Code follows project conventions
- [ ] Tests are included and passing
- [ ] Documentation is updated
- [ ] No console errors or warnings
- [ ] Performance considerations addressed

### 3. Release Process
1. Update version in `pubspec.yaml`
2. Update changelog
3. Run full test suite
4. Build release versions
5. Test on physical devices
6. Deploy to app stores

## Performance Optimization

### 1. Development Tools
```bash
# Analyze performance
flutter run --profile

# Check app size
flutter build apk --analyze-size

# Generate performance report
flutter run --trace-startup --profile
```

### 2. Memory Profiling
- Use Flutter Inspector in IDE
- Monitor memory usage during development
- Profile on physical devices

### 3. Build Optimization
```bash
# Optimize release build
flutter build apk --release --shrink

# Enable obfuscation
flutter build apk --release --obfuscate --split-debug-info=debug-info/
```

## Troubleshooting

### Common Commands
```bash
# Reset Flutter
flutter clean
flutter pub get

# Update dependencies
flutter pub upgrade

# Check for outdated packages
flutter pub outdated

# Analyze code
flutter analyze

# Format code
dart format .
```

### Getting Help
- **Flutter Documentation**: https://docs.flutter.dev/
- **Stack Overflow**: Tag questions with `flutter`
- **Flutter Community**: https://flutter.dev/community
- **GitHub Issues**: Report bugs to Flutter repository

---

This development setup guide provides comprehensive instructions for setting up and running the Agnostic Healthcare Flutter application. Follow these steps to ensure a smooth development experience.
