# State Management Documentation - Agnostic Healthcare

## Overview
This document provides comprehensive documentation for state management implementation in the Agnostic Healthcare Flutter application using the Provider pattern.

## State Management Architecture

### Provider Pattern Implementation
The application uses the **Provider** package for state management, following these principles:
- **Centralized State**: Global app state managed through providers
- **Reactive Updates**: UI automatically updates when state changes
- **Dependency Injection**: Providers injected at app level
- **Separation of Concerns**: Business logic separated from UI

### Provider Setup
```dart
// In main.dart
MultiProvider(
  providers: [
    ChangeNotifierProvider(create: (_) => UserProvider()),
    // Additional providers would be added here
  ],
  child: MaterialApp(
    // App configuration
  ),
)
```

## Core Providers

### 1. UserProvider
Manages user authentication and profile state.

```dart
class UserProvider with ChangeNotifier {
  bool identityVerified = false;
  
  void setVerification(bool status) {
    identityVerified = status;
    notifyListeners();
  }
  
  void login() {
    // Login implementation
    notifyListeners();
  }
  
  void get logout => {
    // Logout implementation
    notifyListeners();
  };
}
```

#### State Properties
- `identityVerified`: <PERSON><PERSON><PERSON> indicating if user identity is verified
- Additional user properties (to be implemented):
  - `user`: User model instance
  - `isLoggedIn`: Authentication status
  - `profileComplete`: Profile completion status

#### Methods
- `setVerification(bool)`: Update identity verification status
- `login()`: Handle user login
- `logout`: Handle user logout
- `updateProfile()`: Update user profile information

### 2. Provider Usage Patterns

#### Accessing Provider Data
```dart
// Using Consumer widget (recommended for specific widgets)
Consumer<UserProvider>(
  builder: (context, userProvider, child) {
    return Text(
      userProvider.identityVerified ? 'Verified' : 'Not Verified',
    );
  },
)

// Using Provider.of (for entire widget rebuilds)
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context);
    return Text(userProvider.identityVerified ? 'Verified' : 'Not Verified');
  }
}

// Using context.watch (in build method)
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final userProvider = context.watch<UserProvider>();
    return Text(userProvider.identityVerified ? 'Verified' : 'Not Verified');
  }
}

// Using context.read (for actions, not in build method)
void handleVerification() {
  context.read<UserProvider>().setVerification(true);
}
```

## State Management Patterns

### 1. Loading States
```dart
class AppointmentProvider with ChangeNotifier {
  bool _isLoading = false;
  List<Appointment> _appointments = [];
  String? _error;
  
  bool get isLoading => _isLoading;
  List<Appointment> get appointments => _appointments;
  String? get error => _error;
  
  Future<void> fetchAppointments() async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      final appointments = await ApiService.getAppointments();
      _appointments = appointments;
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
```

### 2. Form State Management
```dart
class BookingProvider with ChangeNotifier {
  DateTime? _selectedDate;
  TimeOfDay? _selectedTime;
  String? _selectedProvider;
  
  DateTime? get selectedDate => _selectedDate;
  TimeOfDay? get selectedTime => _selectedTime;
  String? get selectedProvider => _selectedProvider;
  
  void setDate(DateTime date) {
    _selectedDate = date;
    notifyListeners();
  }
  
  void setTime(TimeOfDay time) {
    _selectedTime = time;
    notifyListeners();
  }
  
  void setProvider(String provider) {
    _selectedProvider = provider;
    notifyListeners();
  }
  
  bool get canBook => 
    _selectedDate != null && 
    _selectedTime != null && 
    _selectedProvider != null;
  
  void reset() {
    _selectedDate = null;
    _selectedTime = null;
    _selectedProvider = null;
    notifyListeners();
  }
}
```

### 3. Navigation State
```dart
class NavigationProvider with ChangeNotifier {
  int _currentTabIndex = 2; // Default to home tab
  
  int get currentTabIndex => _currentTabIndex;
  
  void changeTab(int index) {
    if (_currentTabIndex != index) {
      _currentTabIndex = index;
      notifyListeners();
    }
  }
  
  void resetToHome() {
    changeTab(2);
  }
}
```

## Advanced State Management

### 1. Computed Properties
```dart
class HealthRecordsProvider with ChangeNotifier {
  List<MedicineRecord> _medications = [];
  List<Immunization> _immunizations = [];
  
  List<MedicineRecord> get medications => _medications;
  List<Immunization> get immunizations => _immunizations;
  
  // Computed property
  int get totalRecords => _medications.length + _immunizations.length;
  
  // Computed property with filtering
  List<MedicineRecord> get activeMedications => 
    _medications.where((med) => med.isActive).toList();
}
```

### 2. State Persistence
```dart
class UserProvider with ChangeNotifier {
  User? _user;
  
  User? get user => _user;
  
  void login(User user) {
    _user = user;
    SharedPrefs().setUserToken(user.token);
    notifyListeners();
  }
  
  void logout() {
    _user = null;
    SharedPrefs().setUserToken('');
    notifyListeners();
  }
  
  // Load user from storage on app start
  Future<void> loadUserFromStorage() async {
    final token = SharedPrefs().savedUserToken;
    if (token.isNotEmpty) {
      try {
        final user = await ApiService.getProfile();
        _user = user;
        notifyListeners();
      } catch (e) {
        // Handle error, possibly logout
        logout();
      }
    }
  }
}
```

### 3. Provider Communication
```dart
class NotificationProvider with ChangeNotifier {
  List<Notification> _notifications = [];
  
  List<Notification> get notifications => _notifications;
  int get unreadCount => _notifications.where((n) => !n.isRead).length;
  
  void addNotification(Notification notification) {
    _notifications.insert(0, notification);
    notifyListeners();
  }
  
  void markAsRead(String notificationId) {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
      notifyListeners();
    }
  }
}

// Usage in another provider
class AppointmentProvider with ChangeNotifier {
  final NotificationProvider _notificationProvider;
  
  AppointmentProvider(this._notificationProvider);
  
  Future<void> bookAppointment(Appointment appointment) async {
    try {
      await ApiService.bookAppointment(appointment);
      _notificationProvider.addNotification(
        Notification(
          title: 'Appointment Booked',
          message: 'Your appointment has been confirmed',
        ),
      );
    } catch (e) {
      // Handle error
    }
  }
}
```

## UI Integration Patterns

### 1. Conditional Rendering
```dart
class HomeScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        if (!userProvider.identityVerified) {
          return VerificationRequiredWidget();
        }
        
        return HomeContent();
      },
    );
  }
}
```

### 2. Loading States in UI
```dart
class AppointmentsList extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<AppointmentProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return CircularLoader();
        }
        
        if (provider.error != null) {
          return ErrorWidget(
            error: provider.error!,
            onRetry: () => provider.fetchAppointments(),
          );
        }
        
        if (provider.appointments.isEmpty) {
          return EmptyStateWidget();
        }
        
        return ListView.builder(
          itemCount: provider.appointments.length,
          itemBuilder: (context, index) {
            return AppointmentCard(
              appointment: provider.appointments[index],
            );
          },
        );
      },
    );
  }
}
```

### 3. Form Integration
```dart
class BookingForm extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<BookingProvider>(
      builder: (context, bookingProvider, child) {
        return Column(
          children: [
            DatePicker(
              selectedDate: bookingProvider.selectedDate,
              onDateSelected: bookingProvider.setDate,
            ),
            TimePicker(
              selectedTime: bookingProvider.selectedTime,
              onTimeSelected: bookingProvider.setTime,
            ),
            RoundActionButton(
              text: 'Book Appointment',
              enabled: bookingProvider.canBook,
              action: () => handleBooking(context),
            ),
          ],
        );
      },
    );
  }
  
  void handleBooking(BuildContext context) {
    final bookingProvider = context.read<BookingProvider>();
    // Use booking data to create appointment
  }
}
```

## Error Handling

### 1. Provider Error States
```dart
class BaseProvider with ChangeNotifier {
  String? _error;
  
  String? get error => _error;
  bool get hasError => _error != null;
  
  void setError(String error) {
    _error = error;
    notifyListeners();
  }
  
  void clearError() {
    _error = null;
    notifyListeners();
  }
  
  Future<T> handleAsyncOperation<T>(Future<T> operation) async {
    try {
      clearError();
      return await operation;
    } catch (e) {
      setError(e.toString());
      rethrow;
    }
  }
}
```

### 2. Global Error Handling
```dart
class ErrorProvider with ChangeNotifier {
  String? _globalError;
  
  String? get globalError => _globalError;
  
  void showError(String error) {
    _globalError = error;
    notifyListeners();
    
    // Auto-clear after 5 seconds
    Timer(Duration(seconds: 5), () {
      clearError();
    });
  }
  
  void clearError() {
    _globalError = null;
    notifyListeners();
  }
}
```

## Testing State Management

### 1. Provider Testing
```dart
void main() {
  group('UserProvider Tests', () {
    late UserProvider userProvider;
    
    setUp(() {
      userProvider = UserProvider();
    });
    
    test('should set verification status', () {
      // Arrange
      expect(userProvider.identityVerified, false);
      
      // Act
      userProvider.setVerification(true);
      
      // Assert
      expect(userProvider.identityVerified, true);
    });
    
    test('should notify listeners on verification change', () {
      // Arrange
      bool notified = false;
      userProvider.addListener(() => notified = true);
      
      // Act
      userProvider.setVerification(true);
      
      // Assert
      expect(notified, true);
    });
  });
}
```

### 2. Widget Testing with Providers
```dart
testWidgets('should display verification status', (tester) async {
  final userProvider = UserProvider();
  
  await tester.pumpWidget(
    ChangeNotifierProvider.value(
      value: userProvider,
      child: MaterialApp(
        home: Consumer<UserProvider>(
          builder: (context, provider, child) {
            return Text(
              provider.identityVerified ? 'Verified' : 'Not Verified',
            );
          },
        ),
      ),
    ),
  );
  
  expect(find.text('Not Verified'), findsOneWidget);
  
  userProvider.setVerification(true);
  await tester.pump();
  
  expect(find.text('Verified'), findsOneWidget);
});
```

## Best Practices

### 1. Provider Organization
- Keep providers focused on specific domains
- Use composition over inheritance
- Implement proper disposal of resources
- Use meaningful names for state properties

### 2. Performance Optimization
- Use `Consumer` widgets for targeted rebuilds
- Implement `Selector` for specific property watching
- Avoid unnecessary `notifyListeners()` calls
- Use `context.read()` for actions outside build method

### 3. State Structure
- Keep state immutable when possible
- Use proper data models
- Implement state validation
- Handle edge cases and error states

### 4. Testing Strategy
- Test provider logic independently
- Mock dependencies for unit tests
- Test UI integration with providers
- Verify state changes and notifications

---

This state management documentation provides comprehensive guidance for implementing and maintaining state in the Agnostic Healthcare application using the Provider pattern. For implementation details, refer to the source files in the `lib/providers/` directory.
