# Quick Reference Guide - Agnostic Healthcare

## 🚀 Getting Started

### Essential Commands
```bash
# Setup
flutter pub get                    # Install dependencies
flutter run                       # Run app in debug mode
flutter run --release             # Run app in release mode

# Development
flutter hot-reload                 # Hot reload (r in terminal)
flutter hot-restart               # Hot restart (R in terminal)
flutter clean                    # Clean build files

# Testing
flutter test                      # Run all tests
flutter test --coverage          # Run tests with coverage
flutter analyze                  # Analyze code for issues

# Building
flutter build apk --release      # Build Android APK
flutter build ios --release      # Build iOS app
flutter build appbundle         # Build Android App Bundle
```

## 📁 Project Structure Quick Reference

```
lib/
├── api/                 # HTTP client & API endpoints
│   ├── api_paths.dart   # API endpoint definitions
│   ├── dio.dart         # HTTP client configuration
│   └── healthcare_dio.dart # Healthcare API services
├── common_screens/      # Shared screens
├── common_widgets/      # Reusable UI components
├── constants/          # App constants & theming
├── models/             # Data models
├── providers/          # State management (Provider pattern)
├── screens/            # App screens organized by feature
├── utils/              # Utilities & helper functions
└── main.dart          # App entry point
```

## 🎨 UI Components Cheat Sheet

### Core Components
```dart
// Card component
AppCard(
  child: Text('Content'),
  bgColor: CustomColors.theme,
  action: () => handleTap(),
)

// Text field
AppTextField(
  hintText: 'Enter text',
  controller: controller,
  validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
)

// Button
RoundActionButton(
  text: 'Continue',
  action: () => handleAction(),
  isLoading: isLoading,
)

// Dropdown
CustomDropdown<String>(
  items: ['Option 1', 'Option 2'],
  value: selectedValue,
  onChanged: (value) => setState(() => selectedValue = value),
)
```

### Layout Helpers
```dart
// Spacing
AppSpacing.verticalXSpacing(space: 16)
AppSpacing.horizontalXSpacing(space: 12)

// Responsive sizing
16.spMin  // Responsive size using ScreenUtil
```

## 🔄 State Management Quick Guide

### Provider Usage
```dart
// Access provider data
Consumer<UserProvider>(
  builder: (context, userProvider, child) {
    return Text(userProvider.identityVerified ? 'Verified' : 'Not Verified');
  },
)

// Read provider for actions (outside build method)
context.read<UserProvider>().setVerification(true);

// Watch provider in build method
final userProvider = context.watch<UserProvider>();
```

### Provider Implementation
```dart
class MyProvider with ChangeNotifier {
  bool _isLoading = false;
  
  bool get isLoading => _isLoading;
  
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
}
```

## 🛣️ Navigation Quick Reference

### Route Navigation
```dart
// Navigate to named route
Navigator.pushNamed(context, Routes.login);

// Navigate with arguments
Navigator.pushNamed(context, Routes.clinicDetail, arguments: clinic);

// Replace current route
Navigator.pushReplacementNamed(context, Routes.bottomNavigation);

// Pop current route
Navigator.pop(context);
```

### Common Routes
```dart
Routes.splash              // Splash screen
Routes.login               // Login screen
Routes.bottomNavigation    // Main app
Routes.bookAppointment     // Appointment booking
Routes.search              // Provider search
Routes.notifications       // Notifications
```

## 🌐 API Integration Quick Guide

### Making API Calls
```dart
// GET request
dio.getApiResponse(
  ApiPaths.profile,
  onSuccess: (message, data) {
    // Handle success
  },
  onError: (error) {
    // Handle error
  },
  timeoutDuration: Duration(seconds: 30),
);

// POST request
dio.postApiResponse(
  ApiPaths.auth,
  requestData: {'email': email, 'password': password},
  onSuccess: (message, data) {
    // Handle success
  },
  onError: (error) {
    // Handle error
  },
  timeoutDuration: Duration(seconds: 30),
);
```

### API Endpoints
```dart
ApiPaths.auth              // Authentication
ApiPaths.profile           // User profile
ApiPaths.updateProfile     // Update profile
ApiPaths.createBagTalk     // Create post
ApiPaths.getAllBagTalkPosts // Get posts
```

## 🎨 Theming Quick Reference

### Colors
```dart
CustomColors.theme         // Primary green #73A127
CustomColors.canvas        // Background #F4F4F2
CustomColors.text          // Text color #262626
CustomColors.textWhite     // White text #FFFFFF
CustomColors.red           // Error red #EF3739
```

### Text Styles
```dart
AppTextStyle.styleSansRegular(16)      // Regular text
AppTextStyle.styleSansMedium(14)       // Medium weight
AppTextStyle.styleSansBold(18)         // Bold text
```

### Border Radius
```dart
AppRoundBorder.circular20px            // 20px radius
AppRoundBorder.circularXpx(pixel: 12)  // Custom radius
```

## 💾 Local Storage Quick Guide

### SharedPreferences Usage
```dart
// Save data
SharedPrefs().setUserToken(token);

// Read data
String token = SharedPrefs().savedUserToken;
bool onboarded = SharedPrefs().onboardingComplete;

// Set flags
SharedPrefs().setOnboardingCompleted;
```

## 🧪 Testing Quick Reference

### Unit Testing
```dart
test('should update verification status', () {
  final provider = UserProvider();
  provider.setVerification(true);
  expect(provider.identityVerified, true);
});
```

### Widget Testing
```dart
testWidgets('should display text', (tester) async {
  await tester.pumpWidget(MyWidget());
  expect(find.text('Hello'), findsOneWidget);
});
```

## 🔧 Common Patterns

### Loading State Pattern
```dart
class MyScreen extends StatefulWidget {
  @override
  _MyScreenState createState() => _MyScreenState();
}

class _MyScreenState extends State<MyScreen> {
  bool isLoading = false;
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: isLoading 
        ? CircularLoader()
        : YourContent(),
    );
  }
}
```

### Error Handling Pattern
```dart
try {
  await apiCall();
} catch (e) {
  AppDialogs.showSimpleDialog(
    context: context,
    title: 'Error',
    detail: e.toString(),
    okButtonText: 'OK',
  );
}
```

### Form Validation Pattern
```dart
final _formKey = GlobalKey<FormState>();

Form(
  key: _formKey,
  child: Column(
    children: [
      AppTextField(
        validator: (value) {
          if (value?.isEmpty ?? true) return 'Required';
          return null;
        },
      ),
      RoundActionButton(
        text: 'Submit',
        action: () {
          if (_formKey.currentState?.validate() ?? false) {
            // Process form
          }
        },
      ),
    ],
  ),
)
```

## 🐛 Debugging Tips

### Common Issues
```bash
# Package conflicts
flutter pub cache repair
rm pubspec.lock
flutter pub get

# Build issues
flutter clean
flutter pub get
flutter run

# iOS specific
cd ios && pod install && cd ..
flutter clean && flutter run
```

### Debug Tools
```dart
// Print debug info
debugPrint('Debug message');

// Check if in debug mode
if (kDebugMode) {
  print('Debug only code');
}
```

## 📱 Platform Specific

### Android
```xml
<!-- Permissions in AndroidManifest.xml -->
<uses-permission android:name="android.permission.INTERNET"/>
<uses-permission android:name="android.permission.CAMERA"/>
```

### iOS
```xml
<!-- Permissions in Info.plist -->
<key>NSCameraUsageDescription</key>
<string>Camera access for document scanning</string>
```

## 🔍 Performance Tips

### Optimization
```dart
// Use const constructors
const Text('Static text')

// Optimize rebuilds with Consumer
Consumer<Provider>(
  builder: (context, provider, child) => Widget(),
)

// Use ListView.builder for large lists
ListView.builder(
  itemCount: items.length,
  itemBuilder: (context, index) => ItemWidget(items[index]),
)
```

---

This quick reference provides essential information for daily development. For detailed documentation, refer to the complete documentation files.
