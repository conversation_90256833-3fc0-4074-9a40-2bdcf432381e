# Navigation Flow Documentation - Agnostic Healthcare

## Overview
This document provides comprehensive documentation for the navigation system and user flow in the Agnostic Healthcare Flutter application.

## Navigation Architecture

### Route Management System
The application uses a centralized route management system with named routes and a custom route generator.

```dart
class Routes {
  // Authentication Routes
  static const String splash = '/';
  static const String login = '/login';
  static const String signup = '/signup';
  static const String onboarding = '/onboarding';
  static const String otp = '/otp';
  
  // Main Application Routes
  static const String bottomNavigation = '/bottom_navigation';
  static const String verifyIdentity = '/verify_identity';
  static const String verifyWithGp = '/verify_with_gp';
  static const String verifyWithHealthCard = '/verify_with_healthcard';
  
  // Feature Routes
  static const String bookAppointment = '/book_appointment';
  static const String search = '/search';
  static const String clinicDetail = '/clinic_detail';
  static const String notifications = '/notifications';
}
```

### Route Generator
```dart
class RouteGenerator {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    Widget widget;
    switch (settings.name) {
      case Routes.splash:
        widget = const SplashScreen();
        break;
      case Routes.login:
        widget = const LoginScreen();
        break;
      // ... additional route cases
      default:
        widget = const SplashScreen();
        break;
    }
    
    return Platform.isIOS
      ? CupertinoPageRoute(builder: (_) => widget)
      : MaterialPageRoute(builder: (_) => widget);
  }
}
```

## Application Flow Diagram

```
┌─────────────────┐
│   Splash Screen │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐    No     ┌─────────────────┐
│ Onboarding      │◄──────────┤ First Time User?│
│ Complete?       │           └─────────────────┘
└─────────┬───────┘
          │ Yes
          ▼
┌─────────────────┐    No     ┌─────────────────┐
│ User Logged In? │──────────►│ Login Screen    │
└─────────┬───────┘           └─────────┬───────┘
          │ Yes                         │
          ▼                             ▼
┌─────────────────┐                   ┌─────────────────┐
│ Identity        │    No             │ OTP Verification│
│ Verified?       │──────────────────►│ Screen          │
└─────────┬───────┘                   └─────────┬───────┘
          │ Yes                                 │
          ▼                                     ▼
┌─────────────────┐                   ┌─────────────────┐
│ Bottom          │                   │ Identity        │
│ Navigation      │◄──────────────────┤ Verification    │
│ (Main App)      │                   │ Flow            │
└─────────────────┘                   └─────────────────┘
```

## Screen Flow Details

### 1. Application Initialization Flow

#### Splash Screen
- **Purpose**: App initialization and route determination
- **Duration**: 3 seconds minimum
- **Logic**:
  ```dart
  if (SharedPrefs().onboardingComplete) {
    if (SharedPrefs().savedUserToken.isNotEmpty) {
      // Navigate to main app or verification
    } else {
      // Navigate to login
    }
  } else {
    // Navigate to onboarding
  }
  ```

#### Onboarding Screen
- **Purpose**: First-time user introduction
- **Features**: App feature showcase, terms acceptance
- **Exit**: Sets onboarding completion flag

### 2. Authentication Flow

#### Login Screen
- **Input Methods**: Phone number or email
- **Validation**: Real-time input validation
- **Actions**: 
  - Login with existing credentials
  - Navigate to signup
  - Forgot password flow

#### Signup Screen
- **Required Fields**: Name, phone, email, password
- **Validation**: Email format, password strength
- **Flow**: Signup → OTP Verification → Identity Verification

#### OTP Verification
- **Types**: 
  - Email OTP (`loginEmailOtp`)
  - Phone OTP (`loginPhoneOtp`)
  - General OTP (`otp`)
- **Features**: Resend OTP, timer countdown
- **Validation**: 6-digit code verification

### 3. Identity Verification Flow

#### Verify Identity Screen
- **Purpose**: Choose verification method
- **Options**:
  - GP (General Practitioner) verification
  - Health card verification
- **Navigation**: Based on user selection

#### GP Verification
- **Route**: `verifyWithGp`
- **Process**: Enter GP details for verification
- **Data**: GP name, practice details, patient ID

#### Health Card Verification
- **Route**: `verifyWithHealthCard`
- **Process**: Scan or enter health card details
- **Features**: Camera integration, OCR processing

### 4. Main Application Navigation

#### Bottom Navigation Structure
The main app uses a bottom navigation with 5 tabs:

```dart
enum BottomNavTab {
  records(0),    // Medical records
  myGp(1),       // GP information
  home(2),       // Dashboard (default)
  providers(3),  // Healthcare providers
  account(4);    // User account
}
```

#### Tab Navigation Logic
```dart
void changeTab(int index) {
  // Prevent access to restricted tabs if not verified
  if ((index == 0 || index == 1) && !userProvider.identityVerified) {
    return;
  }
  
  if (currentTabIndex == index) return;
  
  currentTabIndex = index;
  bottomNavigationTabController.jumpToPage(currentTabIndex);
}
```

#### Back Button Behavior
```dart
PopScope(
  canPop: false,
  onPopInvokedWithResult: (didPop, _) {
    if (!didPop) {
      if (currentTabIndex != 0) {
        changeTab(0); // Navigate to home tab
      }
    }
  },
)
```

## Feature-Specific Navigation

### 1. Healthcare Provider Flow
```
Providers Tab → Search → Provider Detail → Book Appointment
     │              │           │               │
     ▼              ▼           ▼               ▼
Filter Options  Search Results  Practitioner   Appointment
                                Details        Confirmation
```

### 2. Appointment Management Flow
```
Home/Providers → Book Appointment → Select Date/Time → Confirmation
      │               │                    │              │
      ▼               ▼                    ▼              ▼
My Appointments   Provider Selection   Time Slots     Appointment
                                                      Created
```

### 3. Medical Records Flow
```
Records Tab → Medications → Medication Detail
     │             │              │
     ▼             ▼              ▼
Immunizations  Add/Edit Med   View History
```

### 4. Communication Flow
```
Provider Detail → Chat/Call → Active Session
       │             │            │
       ▼             ▼            ▼
   Contact Info   Message UI   End Session
```

## Navigation Utilities

### Global Navigator Key
```dart
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

// Usage in MaterialApp
MaterialApp(
  navigatorKey: navigatorKey,
  onGenerateRoute: RouteGenerator.generateRoute,
)
```

### Navigation Helper Methods
```dart
class NavigationHelper {
  static void pushNamed(String routeName, {Object? arguments}) {
    navigatorKey.currentState?.pushNamed(routeName, arguments: arguments);
  }
  
  static void pushReplacementNamed(String routeName, {Object? arguments}) {
    navigatorKey.currentState?.pushReplacementNamed(routeName, arguments: arguments);
  }
  
  static void pop([Object? result]) {
    navigatorKey.currentState?.pop(result);
  }
  
  static void popUntil(String routeName) {
    navigatorKey.currentState?.popUntil(ModalRoute.withName(routeName));
  }
}
```

## Route Parameters and Arguments

### Passing Arguments
```dart
// Navigate with arguments
Navigator.pushNamed(
  context,
  Routes.clinicDetail,
  arguments: clinicData,
);

// Navigate with complex arguments
Navigator.pushNamed(
  context,
  Routes.otp,
  arguments: {
    'phoneNumber': phoneNumber,
    'isLogin': true,
  },
);
```

### Receiving Arguments
```dart
class ClinicDetailScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final clinic = ModalRoute.of(context)?.settings.arguments as Clinic;
    
    return Scaffold(
      appBar: CustomAppBar(title: clinic.name),
      body: // Clinic details
    );
  }
}
```

### Type-Safe Arguments
```dart
// In route generator
case Routes.viewImage:
  widget = ViewImage(url: settings.arguments as String);
  break;

case Routes.viewImageFile:
  widget = ViewImage(file: settings.arguments as File);
  break;
```

## Deep Linking Support

### Route Handling
```dart
// Handle deep links
class DeepLinkHandler {
  static void handleDeepLink(String link) {
    final uri = Uri.parse(link);
    final path = uri.path;
    
    switch (path) {
      case '/appointment':
        NavigationHelper.pushNamed(Routes.bookAppointment);
        break;
      case '/provider':
        final providerId = uri.queryParameters['id'];
        NavigationHelper.pushNamed(
          Routes.clinicDetail,
          arguments: providerId,
        );
        break;
    }
  }
}
```

## Navigation State Management

### Route Observers
```dart
class AppRouteObserver extends RouteObserver<PageRoute<dynamic>> {
  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    // Track navigation events
    AnalyticsService.trackScreenView(route.settings.name);
  }
}
```

### Navigation Guards
```dart
class AuthGuard {
  static bool canAccess(String routeName) {
    final restrictedRoutes = [
      Routes.bookAppointment,
      Routes.medications,
      Routes.immunizations,
    ];
    
    if (restrictedRoutes.contains(routeName)) {
      return SharedPrefs().savedUserToken.isNotEmpty;
    }
    
    return true;
  }
}
```

## Error Handling

### Route Not Found
```dart
// In RouteGenerator
default:
  return MaterialPageRoute(
    builder: (_) => Scaffold(
      appBar: AppBar(title: Text('Page Not Found')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, size: 64),
            Text('Page not found'),
            ElevatedButton(
              onPressed: () => Navigator.pushReplacementNamed(
                context,
                Routes.splash,
              ),
              child: Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );
```

### Navigation Error Recovery
```dart
class NavigationErrorHandler {
  static void handleNavigationError(dynamic error) {
    // Log error
    debugPrint('Navigation error: $error');
    
    // Fallback navigation
    NavigationHelper.pushReplacementNamed(Routes.splash);
  }
}
```

## Best Practices

### Navigation Guidelines
1. **Use Named Routes**: Always use named routes for consistency
2. **Type Safety**: Use proper argument types and validation
3. **Error Handling**: Implement fallback routes for errors
4. **State Preservation**: Maintain navigation state across app lifecycle
5. **Performance**: Avoid deep navigation stacks

### Route Organization
1. **Logical Grouping**: Group related routes together
2. **Consistent Naming**: Use descriptive route names
3. **Parameter Validation**: Validate route arguments
4. **Documentation**: Document complex navigation flows

### Testing Navigation
```dart
testWidgets('should navigate to login screen', (tester) async {
  await tester.pumpWidget(MyApp());
  
  // Tap login button
  await tester.tap(find.text('Login'));
  await tester.pumpAndSettle();
  
  // Verify navigation
  expect(find.byType(LoginScreen), findsOneWidget);
});
```

---

This navigation documentation provides comprehensive guidance for understanding and implementing navigation flows in the Agnostic Healthcare application. For implementation details, refer to the `lib/utils/routes.dart` file and individual screen implementations.
