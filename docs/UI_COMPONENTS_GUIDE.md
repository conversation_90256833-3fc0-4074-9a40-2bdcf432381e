# UI Components Guide - Agnostic Healthcare

## Overview
This guide provides comprehensive documentation for all custom UI components used in the Agnostic Healthcare Flutter application.

## Design System Principles

### Consistency
- Unified color palette across all components
- Standardized spacing and typography
- Consistent interaction patterns

### Accessibility
- Proper contrast ratios
- Screen reader support
- Touch target sizing

### Responsiveness
- ScreenUtil integration for responsive design
- Adaptive layouts for different screen sizes
- Consistent scaling across devices

## Core Components

### 1. AppCard
A versatile card component that provides consistent styling across the application.

```dart
class AppCard extends StatelessWidget {
  final double? height;
  final Widget child;
  final Color? bgColor;
  final double? padding;
  final double? borderRadius;
  final Gradient? gradient;
  final double elevation;
  final BoxBorder? border;
  final Function()? action;
  final EdgeInsets? insetPadding;
}
```

#### Usage Examples
```dart
// Basic card
AppCard(
  child: Text('Card content'),
)

// Card with custom styling
AppCard(
  bgColor: CustomColors.theme,
  borderRadius: 20,
  elevation: 4,
  action: () => print('Card tapped'),
  child: Column(
    children: [
      Text('Title'),
      Text('Description'),
    ],
  ),
)

// Gradient card
AppCard(
  gradient: AppGradients.stoneBackgroundGradient,
  child: ProfileWidget(),
)
```

#### Features
- **Flexible Styling**: Custom colors, gradients, borders
- **Interactive**: Optional tap handling
- **Consistent Design**: Automatic gradient fallback
- **Responsive**: ScreenUtil integration

### 2. Custom App Bar
Standardized app bar component with consistent styling and behavior.

```dart
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final Color? backgroundColor;
}
```

#### Usage Examples
```dart
// Basic app bar
CustomAppBar(
  title: 'Screen Title',
)

// App bar with actions
CustomAppBar(
  title: 'Providers',
  actions: [
    IconButton(
      icon: Icon(Icons.search),
      onPressed: () => Navigator.pushNamed(context, Routes.search),
    ),
  ],
)

// Custom leading widget
CustomAppBar(
  title: 'Profile',
  leading: BackButton(
    onPressed: () => Navigator.pop(context),
  ),
)
```

### 3. AppTextField
Custom text input field with consistent styling and validation support.

```dart
class AppTextField extends StatelessWidget {
  final String? hintText;
  final String? labelText;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final bool obscureText;
  final TextInputType? keyboardType;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final int? maxLines;
  final bool enabled;
}
```

#### Usage Examples
```dart
// Basic text field
AppTextField(
  hintText: 'Enter your email',
  controller: emailController,
)

// Password field
AppTextField(
  hintText: 'Password',
  obscureText: true,
  suffixIcon: Icon(Icons.visibility),
  validator: (value) {
    if (value?.isEmpty ?? true) {
      return 'Password is required';
    }
    return null;
  },
)

// Multi-line text field
AppTextField(
  hintText: 'Enter description',
  maxLines: 4,
  keyboardType: TextInputType.multiline,
)
```

### 4. RoundActionButton
Primary action button component with consistent styling.

```dart
class RoundActionButton extends StatelessWidget {
  final String text;
  final Function()? action;
  final Color? bgColor;
  final Color? textColor;
  final double? width;
  final double? height;
  final bool isLoading;
  final bool enabled;
}
```

#### Usage Examples
```dart
// Primary button
RoundActionButton(
  text: 'Continue',
  action: () => handleContinue(),
)

// Loading button
RoundActionButton(
  text: 'Saving...',
  isLoading: true,
  action: null,
)

// Custom styled button
RoundActionButton(
  text: 'Cancel',
  bgColor: CustomColors.red,
  textColor: CustomColors.textWhite,
  action: () => Navigator.pop(context),
)
```

### 5. CustomDropdown
Styled dropdown component with consistent appearance.

```dart
class CustomDropdown<T> extends StatelessWidget {
  final List<T> items;
  final T? value;
  final String? hint;
  final Function(T?)? onChanged;
  final String Function(T)? itemBuilder;
  final bool enabled;
}
```

#### Usage Examples
```dart
// Basic dropdown
CustomDropdown<String>(
  items: ['Option 1', 'Option 2', 'Option 3'],
  value: selectedValue,
  hint: 'Select an option',
  onChanged: (value) => setState(() => selectedValue = value),
)

// Custom item builder
CustomDropdown<Provider>(
  items: providers,
  value: selectedProvider,
  hint: 'Select Provider',
  itemBuilder: (provider) => provider.name,
  onChanged: (provider) => handleProviderSelection(provider),
)
```

### 6. ProfileImageWidget
Reusable profile image component with placeholder support.

```dart
class ProfileImageWidget extends StatelessWidget {
  final String? imageUrl;
  final double size;
  final bool showBorder;
  final Function()? onTap;
}
```

#### Usage Examples
```dart
// Basic profile image
ProfileImageWidget(
  imageUrl: user.profileImage,
  size: 60,
)

// Interactive profile image
ProfileImageWidget(
  imageUrl: doctor.profileImage,
  size: 80,
  showBorder: true,
  onTap: () => showDoctorProfile(doctor),
)
```

### 7. CircularLoader
Loading indicator component with consistent styling.

```dart
class CircularLoader extends StatelessWidget {
  final double? size;
  final Color? color;
  final double strokeWidth;
}
```

#### Usage Examples
```dart
// Default loader
CircularLoader()

// Custom sized loader
CircularLoader(
  size: 30,
  color: CustomColors.theme,
)

// In loading dialog
LoadingDialog(
  child: CircularLoader(size: 50),
)
```

## Specialized Components

### 8. AppDialogs
Collection of standardized dialog components.

#### Loading Dialog
```dart
AppDialogs.showLoadingDialog(
  context: context,
  title: 'Processing',
  detail: 'Please wait while we process your request...',
)
```

#### Simple Dialog
```dart
AppDialogs.showSimpleDialog(
  context: context,
  title: 'Confirmation',
  detail: 'Are you sure you want to delete this item?',
  okButtonText: 'Delete',
  cancelButtonText: 'Cancel',
  okButtonFunction: () => handleDelete(),
)
```

### 9. ImagePickerBottomSheet
Bottom sheet for image selection options.

```dart
ImagePickerBottomSheet.show(
  context: context,
  onCameraSelected: () => pickFromCamera(),
  onGallerySelected: () => pickFromGallery(),
)
```

### 10. Time Picker Components
Custom time picker widgets for appointment scheduling.

```dart
TimePicker(
  selectedTime: appointmentTime,
  onTimeChanged: (time) => setState(() => appointmentTime = time),
)
```

## Layout Components

### 11. ExpandableTextWidget
Text widget that can expand/collapse for long content.

```dart
ExpandableTextWidget(
  text: longDescription,
  maxLines: 3,
  expandText: 'Read more',
  collapseText: 'Show less',
)
```

### 12. NetworkImage
Enhanced network image widget with caching and error handling.

```dart
NetworkImage(
  imageUrl: imageUrl,
  placeholder: CircularLoader(),
  errorWidget: Icon(Icons.error),
  fit: BoxFit.cover,
)
```

## Styling Constants

### Colors
```dart
class CustomColors {
  static const Color theme = Color(0xFF73A127);
  static const Color canvas = Color(0xFFF4F4F2);
  static const Color text = Color(0xff262626);
  // ... additional colors
}
```

### Text Styles
```dart
class AppTextStyle {
  static TextStyle styleSansRegular(double size, {Color? fontColor}) =>
    TextStyle(
      fontFamily: 'DMSans-Regular',
      fontSize: size.spMin,
      color: fontColor ?? CustomColors.text,
    );
  
  static TextStyle styleSansMedium(double size, {Color? fontColor}) =>
    TextStyle(
      fontFamily: 'DMSans-Medium',
      fontSize: size.spMin,
      color: fontColor ?? CustomColors.text,
    );
}
```

### Spacing
```dart
class AppSpacing {
  static Widget verticalXSpacing({required double space}) =>
    SizedBox(height: space.spMin);
  
  static Widget horizontalXSpacing({required double space}) =>
    SizedBox(width: space.spMin);
}
```

### Border Radius
```dart
class AppRoundBorder {
  static BorderRadius circular20px = BorderRadius.circular(20.spMin);
  static BorderRadius circularXpx({double pixel = 12}) =>
    BorderRadius.circular(pixel.spMin);
}
```

### Gradients
```dart
class AppGradients {
  static const LinearGradient stoneBackgroundGradient = LinearGradient(
    colors: [Color(0xFFF8F8F6), Color(0xFFEEEEEC)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
}
```

## Component Usage Guidelines

### Best Practices
1. **Consistency**: Always use custom components instead of Flutter defaults
2. **Theming**: Utilize the established color palette and text styles
3. **Responsiveness**: Use ScreenUtil for all sizing (.spMin extension)
4. **Accessibility**: Provide semantic labels and proper contrast
5. **Performance**: Optimize image loading and widget rebuilds

### Common Patterns
```dart
// Standard screen layout
Scaffold(
  appBar: CustomAppBar(title: 'Screen Title'),
  body: Padding(
    padding: EdgeInsets.all(16.spMin),
    child: Column(
      children: [
        AppCard(
          child: // Card content
        ),
        AppSpacing.verticalXSpacing(space: 16),
        RoundActionButton(
          text: 'Action',
          action: () => handleAction(),
        ),
      ],
    ),
  ),
)
```

### Error Handling
```dart
// Handle loading states
if (isLoading) {
  return CircularLoader();
}

// Handle error states
if (hasError) {
  return AppCard(
    child: Column(
      children: [
        Icon(Icons.error, color: CustomColors.red),
        Text('Error occurred'),
        RoundActionButton(
          text: 'Retry',
          action: () => retry(),
        ),
      ],
    ),
  );
}
```

## Testing Components

### Widget Testing
```dart
testWidgets('AppCard should display child widget', (tester) async {
  await tester.pumpWidget(
    MaterialApp(
      home: AppCard(
        child: Text('Test Content'),
      ),
    ),
  );
  
  expect(find.text('Test Content'), findsOneWidget);
});
```

### Integration Testing
- Test component interactions
- Verify responsive behavior
- Test accessibility features
- Validate theming consistency

---

This UI Components Guide provides comprehensive documentation for building consistent and accessible user interfaces in the Agnostic Healthcare application. For implementation details, refer to the source files in the `lib/common_widgets/` directory.
