# API Documentation - Agnostic Healthcare

## Overview
This document provides detailed information about the API layer implementation in the Agnostic Healthcare Flutter application.

## API Architecture

### Base Configuration
The application uses **Dio** as the HTTP client with a centralized configuration approach.

```dart
class AppDio {
  final String baseUrl;
  
  AppDio(this.baseUrl);
  
  // HTTP methods implementation
}
```

### Authentication
All authenticated requests automatically include the Bearer token from SharedPreferences:

```dart
headers: SharedPrefs().savedUserToken.isNotEmpty
  ? {'Authorization': 'Bearer ${SharedPrefs().savedUserToken}'}
  : {}
```

## API Endpoints

### Authentication Endpoints
```dart
class ApiPaths {
  // Authentication
  static String get auth => 'auth';
  static String get updateProfile => 'profile/update';
  static String get profile => 'profile';
  static String get verifyUsername => 'profile/verify-user-name';
}
```

### Profile Management
- **GET** `/profile` - Retrieve user profile
- **POST** `/profile/update` - Update user profile
- **POST** `/profile/verify-user-name` - Verify username availability
- **GET** `/profile/tags` - Get profile tags

### Healthcare Features
- **POST** `/posts/bagtalk/add` - Create healthcare discussion post
- **GET** `/posts/bagtalk` - Get user's posts
- **GET** `/public/posts/bagtalk` - Get all public posts

### Communication Features
- **GET** `/public/posts/bagtalk/get-comments` - Get post comments
- **POST** `/public/posts/bagtalk/add-comment` - Add comment
- **POST** `/public/posts/bagtalk/like` - Like/unlike post
- **GET** `/public/posts/bagtalk/get-likes` - Get post likes

## HTTP Methods

### POST Request
```dart
Future<void> postApiResponse(
  String url, {
  Map<String, dynamic>? requestData,
  Map<String, dynamic>? queryParameters,
  Map<String, dynamic>? headers,
  Function(int, int)? progressReceiver,
  bool convertToFormData = false,
  bool jsonEncodeData = false,
  required Function(String, dynamic) onSuccess,
  required Function(String) onError,
  required Duration timeoutDuration,
}) async {
  // Implementation handles:
  // - Form data conversion
  // - JSON encoding
  // - Progress tracking
  // - Error handling
  // - Timeout management
}
```

### GET Request
```dart
Future<void> getApiResponse(
  String url, {
  Map<String, dynamic>? queryParameters,
  bool convertToFormData = false,
  required Function(String, dynamic) onSuccess,
  required Function(String) onError,
  required Duration timeoutDuration,
}) async {
  // Implementation handles:
  // - Query parameters
  // - Authentication headers
  // - Response parsing
  // - Error handling
}
```

### DELETE Request
```dart
Future<void> deleteApiResponse(
  String url, {
  required Function(String, dynamic) onSuccess,
  required Function(String) onError,
  required Duration timeoutDuration,
}) async {
  // Implementation handles:
  // - Authentication headers
  // - Response validation
  // - Error handling
}
```

## Error Handling

### Standard Error Response
```dart
if (result.statusCode == HttpStatus.ok || result.statusCode == HttpStatus.created) {
  String message = result.data?['message'];
  onSuccess(message, result.data);
} else {
  onError(result.data?['message'] ?? result.statusMessage);
}
```

### Timeout Handling
```dart
.timeout(timeoutDuration, onTimeout: () => timeOutResponse);
```

### Network Error Handling
```dart
on DioException catch (e) {
  if (e.type == DioExceptionType.connectionTimeout ||
      e.type == DioExceptionType.receiveTimeout ||
      e.type == DioExceptionType.sendTimeout) {
    onError('Connection timeout. Please check your internet connection.');
  } else if (e.type == DioExceptionType.connectionError) {
    onError('No internet connection. Please check your network settings.');
  } else {
    onError(e.message ?? 'An unexpected error occurred.');
  }
}
```

## Healthcare-Specific API Service

### BagTalkDio Class
```dart
class BagTalkDio {
  AppDio dio = AppDio('');
  
  // Healthcare-specific API methods would be implemented here
  // Currently contains placeholder for profile retrieval
}
```

## API Usage Examples

### Making a GET Request
```dart
BagTalkDio().getProfile(
  onSuccess: (user) {
    // Handle successful response
    userProvider.login(user);
    Navigator.pushReplacementNamed(context, Routes.bottomNavigation);
  },
  onError: (error) {
    // Handle error response
    showErrorMessage(error: error);
  },
);
```

### Making a POST Request
```dart
dio.postApiResponse(
  ApiPaths.auth,
  requestData: {
    'email': email,
    'password': password,
  },
  onSuccess: (message, data) {
    // Handle successful authentication
    SharedPrefs().setUserToken(data['token']);
  },
  onError: (error) {
    // Handle authentication error
    showErrorDialog(error);
  },
  timeoutDuration: Duration(seconds: 30),
);
```

## Request/Response Flow

### Typical API Call Flow
1. **Prepare Request Data**: Format parameters and headers
2. **Add Authentication**: Inject Bearer token if available
3. **Make HTTP Request**: Execute using Dio client
4. **Handle Response**: Parse success/error responses
5. **Update UI**: Trigger success/error callbacks
6. **Error Recovery**: Implement retry logic if needed

### Response Format
Expected API response structure:
```json
{
  "message": "Success message",
  "data": {
    // Response data object
  },
  "status": 200
}
```

### Error Response Format
```json
{
  "message": "Error description",
  "error": "Error code",
  "status": 400
}
```

## Configuration Constants

### Timeout Durations
```dart
const Duration shortTimeoutDuration = Duration(seconds: 15);
const Duration mediumTimeoutDuration = Duration(seconds: 30);
const Duration longTimeoutDuration = Duration(seconds: 60);
```

### Base URLs
```dart
// Development
const String devBaseUrl = 'https://dev-api.agnostichealthcare.com/';

// Production
const String prodBaseUrl = 'https://api.agnostichealthcare.com/';
```

## Security Considerations

### Token Management
- Tokens are stored securely in SharedPreferences
- Automatic token injection for authenticated requests
- Token refresh logic should be implemented

### Data Validation
- Input sanitization before API calls
- Response validation after API calls
- Proper error message handling

### Network Security
- HTTPS enforcement for all API calls
- Certificate pinning (recommended for production)
- Request/response logging (development only)

## Testing API Integration

### Unit Testing
```dart
test('should make successful API call', () async {
  // Mock Dio response
  when(mockDio.get(any)).thenAnswer((_) async => Response(
    data: {'message': 'Success', 'data': {}},
    statusCode: 200,
  ));
  
  // Test API call
  await apiService.getData(
    onSuccess: (message, data) {
      expect(message, 'Success');
    },
    onError: (error) {
      fail('Should not reach error callback');
    },
  );
});
```

### Integration Testing
- Test complete API flows
- Verify error handling
- Test network connectivity scenarios
- Validate response parsing

## Best Practices

### API Call Implementation
1. Always implement both success and error callbacks
2. Use appropriate timeout durations
3. Handle network connectivity issues
4. Implement loading states in UI
5. Cache responses when appropriate

### Error Handling
1. Provide user-friendly error messages
2. Log detailed errors for debugging
3. Implement retry mechanisms
4. Handle offline scenarios

### Performance Optimization
1. Use connection pooling
2. Implement request caching
3. Optimize payload sizes
4. Use compression when available

---

This API documentation provides comprehensive guidance for working with the healthcare application's API layer. For implementation details, refer to the source files in the `lib/api/` directory.
