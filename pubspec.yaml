name: agnostic_healthcare
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+5

environment:
  sdk: ^3.7.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  provider: ^6.1.2
  flutter_screenutil: ^5.9.3
  fluttertoast: ^8.2.6
  dio: ^5.5.0+1
  shared_preferences: ^2.3.3
  flutter_svg: ^2.0.10+1
  extended_image: ^9.0.7
  circular_profile_avatar: ^2.0.5
  dropdown_button2: ^2.3.9
  expandable_page_view: ^1.0.17
  intl: any
  json_annotation: ^4.7.0
  path_provider: any
  shimmer: ^3.0.0
  permission_handler: ^12.0.0+1
  image_picker: ^1.1.2
  flutter_exif_rotation: ^0.5.1
  timeago: ^3.7.1
  table_calendar: ^3.1.2
  flutter_toggle_tab: ^1.5.1
  gradient_borders: ^1.0.1
  mask_text_input_formatter: ^2.9.0
  flutter_spinkit: ^5.2.2
  pinput: ^5.0.1
  camera: ^0.11.2
  flutter_decorated_container: ^1.0.0
  grouped_list: ^6.0.0
  cached_network_image: any

dev_dependencies:
  change_app_package_name: ^1.5.0
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/icons/
    - assets/images/
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: DMSans-Bold
      fonts:
        - asset: assets/fonts/DMSans-Bold.ttf
    - family: DMSans-ExtraLight
      fonts:
        - asset: assets/fonts/DMSans-ExtraLight.ttf
    - family: DMSans-Regular
      fonts:
        - asset: assets/fonts/DMSans-Regular.ttf
    - family: DMSans-Medium
      fonts:
        - asset: assets/fonts/DMSans-Medium.ttf
    - family: DMSans-SemiBold
      fonts:
        - asset: assets/fonts/DMSans-SemiBold.ttf
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
